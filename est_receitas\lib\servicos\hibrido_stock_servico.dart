import '../models/item_despensa.dart';
import 'servico_armazenamento.dart';
import 'package:flutter/foundation.dart';
import 'api_stock_servico.dart';  // Adicionado para integração com backend
import 'conectividade_servico.dart'; // Adicionado para verificar conectividade

// Serviço híbrido de stock (local + backend)
class HibridoStockServico {
  static const String _keyItens = 'itens_despensa_locais';
  
  // Serviços para integração com backend
  final ApiStockServico _apiServico = ApiStockServico();
  final ConectividadeServico _conectividadeServico = ConectividadeServico();

  // ===== OPERAÇÕES BÁSICAS =====

  // Obter todos os itens
  Future<List<ItemDespensa>> obterTodosItens() async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Se tem conexão, tentar obter do backend primeiro
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando obter itens do backend...');
          final itensBackend = await _apiServico.obterTodosItens();
          debugPrint('✅ Obtidos ${itensBackend.length} itens do backend');
          
          // Salvar localmente para uso offline
          await _guardarItens(itensBackend);
          
          return itensBackend;
        } catch (apiError) {
          debugPrint('⚠️ Não foi possível obter itens do backend: $apiError');
          // Se falhar, continua para carregar do armazenamento local
        }
      }
      
      // Se não tem conexão ou falhou no backend, carregar do armazenamento local
      final itens = await ServicoArmazenamento.loadObjectList<ItemDespensa>(
        _keyItens,
        (map) => ItemDespensa.fromMap(map),
      );
      
      // Se não há itens, criar alguns de exemplo
      if (itens.isEmpty) {
        return await _criarItensExemplo();
      }
      
      return itens;
    } catch (e) {
      debugPrint('Erro ao obter itens: $e');
      return await _criarItensExemplo();
    }
  }

  // Obter item por ID
  Future<ItemDespensa?> obterItemPorId(int id) async {
    try {
      final itens = await obterTodosItens();
      return itens.firstWhere(
        (item) => item.id == id,
        orElse: () => throw Exception('Item não encontrado'),
      );
    } catch (e) {
      debugPrint('Erro ao obter item por ID: $e');
      return null;
    }
  }

  // Adicionar novo item
  Future<ItemDespensa> adicionarItem(ItemDespensa item) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Tentar salvar no backend primeiro, se houver conexão
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando salvar item no backend...');
          final itemSalvo = await _apiServico.adicionarItem(item);
          debugPrint('✅ Item salvo com sucesso no backend!');
          
          // Também guardar localmente a versão do backend
          final itens = await obterTodosItens();
          final index = itens.indexWhere((i) => i.id == itemSalvo.id);
          if (index >= 0) {
            itens[index] = itemSalvo;
          } else {
            itens.add(itemSalvo);
          }
          await _guardarItens(itens);
          
          return itemSalvo;
        } catch (apiError) {
          debugPrint('❌ Erro ao salvar no backend: $apiError');
          // Se falhar no backend, continua para salvar apenas localmente
        }
      }
      
      // Se não tem conexão ou falhou no backend, salvar apenas localmente
      final itens = await obterTodosItens();
      
      // Se é um item novo, gerar ID
      if (item.id == null) {
        item = item.copyWith(
          id: DateTime.now().millisecondsSinceEpoch,
        );
      }
      
      // Verificar se já existe
      final index = itens.indexWhere((i) => i.id == item.id);
      if (index >= 0) {
        itens[index] = item;
      } else {
        itens.add(item);
      }
      
      await _guardarItens(itens);
      debugPrint('💾 Item salvo apenas localmente');
      return item;
    } catch (e) {
      throw Exception('Erro ao adicionar item: $e');
    }
  }

  // Atualizar item
  Future<ItemDespensa> atualizarItem(ItemDespensa item) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Se tem conexão e o item tem ID, tentar atualizar no backend
      if (temConexao && item.id != null) {
        try {
          debugPrint('🌐 Tentando atualizar item no backend...');
          final itemAtualizado = await _apiServico.atualizarItem(item);
          debugPrint('✅ Item atualizado com sucesso no backend!');
          
          // Também atualizar localmente
          final itens = await obterTodosItens();
          final index = itens.indexWhere((i) => i.id == itemAtualizado.id);
          if (index >= 0) {
            itens[index] = itemAtualizado;
          } else {
            itens.add(itemAtualizado);
          }
          await _guardarItens(itens);
          
          return itemAtualizado;
        } catch (apiError) {
          debugPrint('❌ Erro ao atualizar no backend: $apiError');
          // Se falhar no backend, continua para atualizar apenas localmente
        }
      }
      
      // Se não tem conexão, falhou no backend ou é um novo item, usar lógica de adicionar
      return await adicionarItem(item);
    } catch (e) {
      throw Exception('Erro ao atualizar item: $e');
    }
  }

  // Atualizar quantidade de um item
  Future<ItemDespensa> atualizarQuantidade(int id, double novaQuantidade) async {
    try {
      final item = await obterItemPorId(id);
      if (item == null) {
        throw Exception('Item não encontrado');
      }

      final itemAtualizado = item.copyWith(quantidade: novaQuantidade);
      return await atualizarItem(itemAtualizado);
    } catch (e) {
      throw Exception('Erro ao atualizar quantidade: $e');
    }
  }

  // Eliminar item
  Future<bool> eliminarItem(int id) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Tentar eliminar do backend primeiro, se houver conexão
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando eliminar item do backend...');
          final sucesso = await _apiServico.removerItem(id);
          if (sucesso) {
            debugPrint('✅ Item eliminado com sucesso do backend!');
          } else {
            debugPrint('⚠️ Backend não encontrou o item para eliminar');
          }
        } catch (apiError) {
          debugPrint('❌ Erro ao eliminar do backend: $apiError');
          // Se falhar no backend, continua para eliminar apenas localmente
        }
      }
      
      // Sempre eliminar localmente, mesmo se falhar no backend
      final itens = await obterTodosItens();
      final index = itens.indexWhere((i) => i.id == id);
      
      if (index >= 0) {
        itens.removeAt(index);
        await _guardarItens(itens);
        debugPrint('💾 Item eliminado localmente');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Erro ao eliminar item: $e');
      return false;
    }
  }

  // Obter itens próximos do vencimento
  Future<List<ItemDespensa>> obterItensProximosDoVencimento({int dias = 7}) async {
    try {
      final itens = await obterTodosItens();

      return itens.toList();
    } catch (e) {
      debugPrint('Erro ao obter itens próximos do vencimento: $e');
      return [];
    }
  }

  // Obter itens vencidos
  Future<List<ItemDespensa>> obterItensVencidos() async {
    try {
      final itens = await obterTodosItens();

      return itens.toList();
    } catch (e) {
      debugPrint('Erro ao obter itens vencidos: $e');
      return [];
    }
  }

  // Pesquisar itens por nome
  Future<List<ItemDespensa>> pesquisarItens(String nome) async {
    try {
      final itens = await obterTodosItens();
      final nomeLower = nome.toLowerCase();
      
      return itens.where((item) {
        return item.nome.toLowerCase().contains(nomeLower);
      }).toList();
    } catch (e) {
      debugPrint('Erro ao pesquisar itens: $e');
      return [];
    }
  }

  // Contar total de itens
  Future<int> contarItens() async {
    try {
      final itens = await obterTodosItens();
      return itens.length;
    } catch (e) {
      debugPrint('Erro ao contar itens: $e');
      return 0;
    }
  }

  // Obter itens por localização
  Future<List<ItemDespensa>> obterItensPorLocalizacao(LocalizacaoItem localizacao) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;

      // Se tem conexão, tentar obter do backend primeiro
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando obter itens por localização do backend...');
          List<ItemDespensa> itensBackend;

          // Chamar o método específico do backend de acordo com a localização
          if (localizacao == LocalizacaoItem.despensa) {
            itensBackend = await _apiServico.obterItensDespensa();
          } else if (localizacao == LocalizacaoItem.frigorifico) {
            itensBackend = await _apiServico.obterItensFrigorifico();
          } else {
            // Fallback para filtrar todos os itens
            itensBackend = (await _apiServico.obterTodosItens())
                .where((item) => item.localizacao == localizacao)
                .toList();
          }

          debugPrint('✅ Obtidos ${itensBackend.length} itens do backend por localização');
          return itensBackend;
        } catch (apiError) {
          debugPrint('⚠️ Não foi possível obter itens por localização do backend: $apiError');
          // Se falhar, continua para carregar do armazenamento local
        }
      }

      // Se não tem conexão ou falhou no backend, filtrar dos itens locais
      final itens = await obterTodosItens();
      return itens.where((item) => item.localizacao == localizacao).toList();
    } catch (e) {
      debugPrint('Erro ao obter itens por localização: $e');
      return [];
    }
  }

  // ===== MÉTODOS AUXILIARES =====

  // Guardar lista de itens
  Future<void> _guardarItens(List<ItemDespensa> itens) async {
    try {
      await ServicoArmazenamento.saveObjectList(
        _keyItens,
        itens,
        (item) => item.toMap(),
      );
    } catch (e) {
      throw Exception('Erro ao guardar itens: $e');
    }
  }

  // Criar itens de exemplo
  Future<List<ItemDespensa>> _criarItensExemplo() async {
    final itens = [
      ItemDespensa(
        id: 1,
        nome: 'Arroz',
        quantidade: 2.0,
        unidade: 'kg',
        localizacao: LocalizacaoItem.despensa,
      ),
      ItemDespensa(
        id: 2,
        nome: 'Leite',
        quantidade: 1.0,
        unidade: 'L',
        localizacao: LocalizacaoItem.frigorifico,
      ),
      ItemDespensa(
        id: 3,
        nome: 'Ovos',
        quantidade: 12.0,
        unidade: 'unidades',
        localizacao: LocalizacaoItem.frigorifico,
      ),
      ItemDespensa(
        id: 4,
        nome: 'Azeite',
        quantidade: 0.5,
        unidade: 'L',
        localizacao: LocalizacaoItem.despensa,
      ),
      ItemDespensa(
        id: 5,
        nome: 'Tomate',
        quantidade: 3.0,
        unidade: 'unidades',
        localizacao: LocalizacaoItem.frigorifico,
      ),
    ];

    await _guardarItens(itens);
    return itens;
  }
}
