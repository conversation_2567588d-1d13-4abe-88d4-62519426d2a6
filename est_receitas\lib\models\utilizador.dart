// Modelo de dados para um utilizador
class Utilizador {
  final String? id;
  final String nome;
  final String email;
  final String? telefone;
  final String? password; // Para autenticação local
  final String? token;    // Token para autenticação com o backend


  // Construtor
  const Utilizador({
    this.id,
    required this.nome,
    required this.email,
    this.telefone,
    this.password,
    this.token,
  });

  // Cria uma cópia do utilizador com campos alterados
  // Permite atualizar atributos específicos mantendo os outros inalterados
  Utilizador copyWith({
    String? id,
    String? nome,
    String? email,
    String? telefone,
    String? avatarUrl,
    String? password,
    String? token,
    DateTime? dataCriacao,
    DateTime? dataUltimoAcesso,
    bool? isAtivo,
    TipoUtilizador? tipo,
  }) {
    return Utilizador(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      email: email ?? this.email,
      telefone: telefone ?? this.telefone,
      password: password ?? this.password,
      token: token ?? this.token,
    );
  }

  // Converte o utilizador para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nome': nome,
      'email': email,
      'telefone': telefone,
      'password': password,
      'token': token,
    };
  }
  // Converte o utilizador para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'email': email,
      'telefone': telefone,
      'token': token,
    };
  }
  // Cria um utilizador a partir de um Map (armazenamento local)
  factory Utilizador.fromMap(Map<String, dynamic> map) {
    return Utilizador(
      id: map['id']?.toString(),
      nome: map['nome'] ?? '',
      email: map['email'] ?? '',
      telefone: map['telefone'],
      password: map['password'],
      token: map['token'],
    );
  }

  // Cria um utilizador a partir de JSON (API)
  factory Utilizador.fromJson(Map<String, dynamic> json) {
    return Utilizador(
      id: json['id']?.toString(),
      nome: json['nome'] ?? '',
      email: json['email'] ?? '',
      telefone: json['telefone'],
    );
  }

  @override
  String toString() {
    return 'Utilizador(id: $id, nome: $nome, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Utilizador &&
      other.id == id &&
      other.nome == nome &&
      other.email == email &&
      other.telefone == telefone;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      nome.hashCode ^
      email.hashCode ^
      telefone.hashCode;
  }
}

// Tipos de utilizador
enum TipoUtilizador {
  utilizador,
  administrador,
  moderador,
}

// Compatibilidade
typedef User = Utilizador;
typedef UserType = TipoUtilizador;
