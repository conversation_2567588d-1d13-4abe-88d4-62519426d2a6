/// Modelo de dados para um utilizador
class Utilizador {
  final String? id;
  final String nome;
  final String email;
  final String? telefone;
  final String? password; // Para autenticação local
  final String? token;    // Token para autenticação com o backend
  final PreferenciasUtilizador? preferencias;


  // Construtor
  const Utilizador({
    this.id,
    required this.nome,
    required this.email,
    this.telefone,
    this.password,
    this.token,
    this.preferencias,
  });

  /// Cria uma cópia do utilizador com campos alterados
  /// Permite atualizar atributos específicos mantendo os outros inalterados
  Utilizador copyWith({
    String? id,
    String? nome,
    String? email,
    String? telefone,
    String? avatarUrl,
    String? password,
    String? token,
    DateTime? dataCriacao,
    DateTime? dataUltimoAcesso,
    bool? isAtivo,
    TipoUtilizador? tipo,
    PreferenciasUtilizador? preferencias,
  }) {
    return Utilizador(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      email: email ?? this.email,
      telefone: telefone ?? this.telefone,
      password: password ?? this.password,
      token: token ?? this.token,
      preferencias: preferencias ?? this.preferencias,
    );
  }

  /// Converte o utilizador para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nome': nome,
      'email': email,
      'telefone': telefone,
      'password': password,
      'token': token,
      'preferencias': preferencias?.toMap(),
    };
  }
  /// Converte o utilizador para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'email': email,
      'telefone': telefone,
      'token': token,
      'preferencias': preferencias?.toJson(),
    };
  }
  /// Cria um utilizador a partir de um Map (armazenamento local)
  factory Utilizador.fromMap(Map<String, dynamic> map) {
    return Utilizador(
      id: map['id']?.toString(),
      nome: map['nome'] ?? '',
      email: map['email'] ?? '',
      telefone: map['telefone'],
      password: map['password'],
      token: map['token'],
      preferencias: map['preferencias'] != null
          ? PreferenciasUtilizador.fromMap(map['preferencias'])
          : null,
    );
  }

  /// Cria um utilizador a partir de JSON (API)
  factory Utilizador.fromJson(Map<String, dynamic> json) {
    return Utilizador(
      id: json['id']?.toString(),
      nome: json['nome'] ?? '',
      email: json['email'] ?? '',
      telefone: json['telefone'],
      preferencias: json['preferencias'] != null 
          ? PreferenciasUtilizador.fromJson(json['preferencias']) 
          : null,
    );
  }

  @override
  String toString() {
    return 'Utilizador(id: $id, nome: $nome, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Utilizador &&
      other.id == id &&
      other.nome == nome &&
      other.email == email &&
      other.telefone == telefone &&
      other.preferencias == preferencias;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      nome.hashCode ^
      email.hashCode ^
      telefone.hashCode ^
      preferencias.hashCode;
  }
}

/// Tipos de utilizador
enum TipoUtilizador {
  utilizador,
  administrador,
  moderador,
}


/// Preferências do utilizador
class PreferenciasUtilizador {
  final bool notificacoesAtivas;
  final bool notificacoesValidade;
  final bool notificacoesStockBaixo;
  final bool notificacoesSugestoes;
  final String? temaPreferido;
  final String? idiomaPreferido;
  final List<String> categoriasPreferidas;
  final List<String> ingredientesEvitar;

  const PreferenciasUtilizador({
    this.notificacoesAtivas = true,
    this.notificacoesValidade = true,
    this.notificacoesStockBaixo = true,
    this.notificacoesSugestoes = true,
    this.temaPreferido,
    this.idiomaPreferido,
    this.categoriasPreferidas = const [],
    this.ingredientesEvitar = const [],
  });

  PreferenciasUtilizador copyWith({
    bool? notificacoesAtivas,
    bool? notificacoesValidade,
    bool? notificacoesStockBaixo,
    bool? notificacoesSugestoes,
    String? temaPreferido,
    String? idiomaPreferido,
    List<String>? categoriasPreferidas,
    List<String>? ingredientesEvitar,
  }) {
    return PreferenciasUtilizador(
      notificacoesAtivas: notificacoesAtivas ?? this.notificacoesAtivas,
      notificacoesValidade: notificacoesValidade ?? this.notificacoesValidade,
      notificacoesStockBaixo: notificacoesStockBaixo ?? this.notificacoesStockBaixo,
      notificacoesSugestoes: notificacoesSugestoes ?? this.notificacoesSugestoes,
      temaPreferido: temaPreferido ?? this.temaPreferido,
      idiomaPreferido: idiomaPreferido ?? this.idiomaPreferido,
      categoriasPreferidas: categoriasPreferidas ?? this.categoriasPreferidas,
      ingredientesEvitar: ingredientesEvitar ?? this.ingredientesEvitar,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'notificacoesAtivas': notificacoesAtivas,
      'notificacoesValidade': notificacoesValidade,
      'notificacoesStockBaixo': notificacoesStockBaixo,
      'notificacoesSugestoes': notificacoesSugestoes,
      'temaPreferido': temaPreferido,
      'idiomaPreferido': idiomaPreferido,
      'categoriasPreferidas': categoriasPreferidas,
      'ingredientesEvitar': ingredientesEvitar,
    };
  }

  Map<String, dynamic> toJson() => toMap();

  factory PreferenciasUtilizador.fromMap(Map<String, dynamic> map) {
    return PreferenciasUtilizador(
      notificacoesAtivas: map['notificacoesAtivas'] ?? true,
      notificacoesValidade: map['notificacoesValidade'] ?? true,
      notificacoesStockBaixo: map['notificacoesStockBaixo'] ?? true,
      notificacoesSugestoes: map['notificacoesSugestoes'] ?? true,
      temaPreferido: map['temaPreferido'],
      idiomaPreferido: map['idiomaPreferido'],
      categoriasPreferidas: List<String>.from(map['categoriasPreferidas'] ?? []),
      ingredientesEvitar: List<String>.from(map['ingredientesEvitar'] ?? []),
    );
  }

  factory PreferenciasUtilizador.fromJson(Map<String, dynamic> json) => 
      PreferenciasUtilizador.fromMap(json);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is PreferenciasUtilizador &&
      other.notificacoesAtivas == notificacoesAtivas &&
      other.notificacoesValidade == notificacoesValidade &&
      other.notificacoesStockBaixo == notificacoesStockBaixo &&
      other.notificacoesSugestoes == notificacoesSugestoes &&
      other.temaPreferido == temaPreferido &&
      other.idiomaPreferido == idiomaPreferido &&
      other.categoriasPreferidas == categoriasPreferidas &&
      other.ingredientesEvitar == ingredientesEvitar;
  }

  @override
  int get hashCode {
    return notificacoesAtivas.hashCode ^
      notificacoesValidade.hashCode ^
      notificacoesStockBaixo.hashCode ^
      notificacoesSugestoes.hashCode ^
      temaPreferido.hashCode ^
      idiomaPreferido.hashCode ^
      categoriasPreferidas.hashCode ^
      ingredientesEvitar.hashCode;
  }
}

// Compatibilidade
typedef User = Utilizador;
typedef UserType = TipoUtilizador;
typedef UserPreferences = PreferenciasUtilizador;
