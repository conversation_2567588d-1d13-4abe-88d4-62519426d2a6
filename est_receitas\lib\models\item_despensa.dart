// Modelo de dados para itens da despensa

/// Define os possíveis locais onde um item pode ser armazenado
enum LocalizacaoItem {
  despensa('Despensa'),
  frigorifico('Frigorifico');

  // Construtor que recebe o nome para exibição
  const LocalizacaoItem(this.nomeExibicao);
  // Nome para exibição na interface
  final String nomeExibicao;
}

/// Modelo de dados para um item da despensa ou frigorifico
/// Representa alimentos e ingredientes armazenados na cozinha do utilizador
/// com detalhes como quantidade, validade e localização
class ItemDespensa {
  final int? id;
  final String nome;
  final double quantidade;
  final String unidade;
  final String? categoria;
  final DateTime? dataValidade;
  final LocalizacaoItem localizacao;
  
  ItemDespensa({
    this.id,
    required this.nome,
    required this.quantidade,
    required this.unidade,
    this.categoria,
    this.dataValidade,
    required this.localizacao,
  });
  
  /// Cria uma cópia do item com campos alterados
  /// Atualizar apenas alguns atributos mantendo os demais inalterados
  ItemDespensa copyWith({
    int? id,
    String? nome,
    double? quantidade,
    String? unidade,
    String? categoria,    
    DateTime? dataValidade,
    LocalizacaoItem? localizacao,
  }) {
    return ItemDespensa(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      quantidade: quantidade ?? this.quantidade,
      unidade: unidade ?? this.unidade,
      categoria: categoria ?? this.categoria,      
      dataValidade: dataValidade ?? this.dataValidade,
      localizacao: localizacao ?? this.localizacao,
    );
  }

    /// Verifica se o item está próximo do vencimento (7 dias por padrão)
  bool estaProximoDoVencimento({int dias = 7}) {
    if (dataValidade == null) return false;
    final agora = DateTime.now();
    final diferenca = dataValidade!.difference(agora).inDays;
    return diferenca <= dias && diferenca >= 0;
  }

  /// Verifica se o item está vencido
  bool get estaVencido {
    if (dataValidade == null) return false;
    return dataValidade!.isBefore(DateTime.now());
  }

  /// Retorna dias para vencer (negativo se já vencido)
  int? get diasParaVencer {
    if (dataValidade == null) return null;
    return dataValidade!.difference(DateTime.now()).inDays;
  }



  /// Retorna uma representação legível da quantidade
  String get quantidadeExibicao {
    if (quantidade == quantidade.toInt()) {
      return '${quantidade.toInt()} $unidade';
    } else {
      return '${quantidade.toStringAsFixed(1)} $unidade';
    }
  }

  /// Retorna quantidade formatada
  String get quantidadeFormatada {
    if (quantidade == quantidade.toInt()) {
      return quantidade.toInt().toString();
    } else {
      return quantidade.toStringAsFixed(1);
    }
  }

  /// Retorna descrição completa do item
  String get descricaoCompleta {
    String descricao = nome;
    descricao += ' - $quantidadeExibicao';
    return descricao;
  }

  /// Converte o item para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'dataValidade': dataValidade?.toIso8601String(),
      if (categoria != null) 'categoria': categoria,
      'localizacao': localizacao.name,
    };
  }

  /// Converte o item para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'dataValidade': dataValidade?.toIso8601String().split('T')[0], // Apenas data
      if (categoria != null) 'categoria': categoria,
      'localizacao': localizacao == LocalizacaoItem.despensa ? 'DESPENSA' : 'FRIGORIFICO',
    };
  }

  /// Cria um item a partir de um Map (armazenamento local)
  factory ItemDespensa.fromMap(Map<String, dynamic> map) {
    return ItemDespensa(
      id: map['id'],
      nome: map['nome'] ?? '',
      quantidade: map['quantidade']?.toDouble() ?? 0.0,
      unidade: map['unidade'] ?? '',
      dataValidade: map['dataValidade'] != null ? DateTime.parse(map['dataValidade']) : null,
      categoria: map['categoria'],
      localizacao: LocalizacaoItem.values.firstWhere(
        (e) => e.name == map['localizacao'],
        orElse: () => LocalizacaoItem.despensa,
      ),
    );
  }

  /// Cria um item a partir de JSON (API)
  factory ItemDespensa.fromJson(Map<String, dynamic> json) {
    return ItemDespensa(
      id: json['id'],
      nome: json['nome'] ?? '',
      quantidade: json['quantidade']?.toDouble() ?? 0.0,
      unidade: json['unidade'] ?? '',
      dataValidade: json['dataValidade'] != null ? DateTime.parse(json['dataValidade']) : null,
      categoria: json['categoria'],
      localizacao: json['localizacao'] == 'DESPENSA'
          ? LocalizacaoItem.despensa
          : LocalizacaoItem.frigorifico,
    );
  }

  @override
  String toString() {
    return 'ItemDespensa(id: $id, nome: $nome, quantidade: $quantidade, unidade: $unidade, localizacao: $localizacao)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is ItemDespensa &&
      other.id == id &&
      other.nome == nome &&
      other.quantidade == quantidade &&
      other.unidade == unidade &&      
      other.dataValidade == dataValidade &&
      other.localizacao == localizacao;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      nome.hashCode ^
      quantidade.hashCode ^
      unidade.hashCode ^
      dataValidade.hashCode ^
      localizacao.hashCode;
  }
}

// Compatibilidade com código existente
typedef PantryItem = ItemDespensa;
typedef ItemLocation = LocalizacaoItem;
