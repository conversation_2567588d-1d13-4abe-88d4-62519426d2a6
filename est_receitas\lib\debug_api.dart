import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'models/receita.dart';
import 'models/ingrediente.dart';

/// Utilitário para depuração de problemas com o backend
class DebugApi {
  static const String baseUrl = 'http://localhost:8080/api';
  /// Testar criação de receita diretamente, sem usar o serviço
  static Future<bool> testarCriacaoReceita() async {
    try {
      // Criar uma receita de teste simples com o formato exato esperado pelo backend
      final receita = {
        'titulo': 'Receita de Teste API',
        'descricao': 'Receita de teste para debugar API',
        'instrucoes': 'Instruções de teste',
        'tempoPreparo': 30,
        'numeroPorcoes': 2,
        'dificuldade': 'FACIL', // Enum no backend: FACIL, MEDIO, DIFICIL
        'categoria': 'TESTE',
        'ingredientes': [
          {
            'nome': 'Ingrediente Teste',
            'quantidade': 1.0,
            'unidade': 'unidade',
            'observacoes': 'Observação de teste'
          }
        ]
      };

      // Enviar diretamente para a API
      final body = json.encode(receita);
      debugPrint('🔍 Enviando receita de teste para o backend');
      debugPrint('🔍 Body: $body');
      
      final response = await http.post(
        Uri.parse('$baseUrl/receitas'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: body,
      );

      debugPrint('🔍 Status code: ${response.statusCode}');
      debugPrint('🔍 Response body: ${response.body}');

      return response.statusCode == 201;
    } catch (e) {
      debugPrint('❌ Erro ao testar criação de receita: $e');
      return false;
    }
  }

  /// Testar conectividade com o backend
  static Future<bool> testarConectividade() async {
    try {
      debugPrint('🔍 Testando conectividade com o backend');
      
      final response = await http.get(
        Uri.parse('$baseUrl/testes/saude'),
        headers: {
          'Accept': 'application/json',
        },
      );

      debugPrint('🔍 Status code: ${response.statusCode}');
      if (response.statusCode == 200) {
        debugPrint('✅ Backend acessível!');
      } else {
        debugPrint('❌ Backend não está respondendo corretamente');
      }

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Erro de conectividade: $e');
      return false;
    }
  }

  /// Testar API com detalhes completos
  static Future<void> testarApiDetalhada() async {
    try {
      debugPrint('🔍 TESTE DETALHADO DA API DE RECEITAS');
      debugPrint('🔍 Testando conexão com servidor...');
      
      // 1. Testar conectividade básica
      final conectado = await testarConectividade();
      if (!conectado) {
        debugPrint('❌ Falha na conexão com servidor. Abortando testes.');
        return;
      }
      
      // 2. Listar todas as receitas atuais
      try {
        debugPrint('🔍 Listando todas as receitas existentes...');
        final response = await http.get(
          Uri.parse('$baseUrl/receitas'),
          headers: {'Accept': 'application/json'},
        );
        debugPrint('📊 Status: ${response.statusCode}');
        debugPrint('📊 Conteúdo: ${response.body}');
        
        final List<dynamic> receitasAtuais = json.decode(response.body);
        debugPrint('📊 Número de receitas existentes: ${receitasAtuais.length}');
      } catch (e) {
        debugPrint('❌ Erro ao listar receitas: $e');
      }
      
      // 3. Testar criação com formato específico
      try {
        debugPrint('🔍 Criando uma nova receita com formato específico...');
        final receitaTeste = {
          'titulo': 'Bolo de Chocolate Especial',
          'descricao': 'Um delicioso bolo de chocolate',
          'instrucoes': '1. Misture os ingredientes secos.\n2. Adicione os líquidos.\n3. Asse por 40 minutos.',
          'tempoPreparo': 60,
          'numeroPorcoes': 8,
          'dificuldade': 'MEDIO', // Exatamente como o enum no backend
          'categoria': 'SOBREMESAS',
          'ingredientes': [
            {
              'nome': 'Farinha de trigo',
              'quantidade': 2.0,
              'unidade': 'xícaras',
              'observacoes': null
            },
            {
              'nome': 'Chocolate em pó',
              'quantidade': 1.0,
              'unidade': 'xícara',
              'observacoes': 'Peneirado'
            },
            {
              'nome': 'Ovos',
              'quantidade': 3.0,
              'unidade': 'unidades',
              'observacoes': 'Em temperatura ambiente'
            }
          ]
        };
        
        final body = json.encode(receitaTeste);
        debugPrint('📤 Payload JSON: $body');
        
        final response = await http.post(
          Uri.parse('$baseUrl/receitas'),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: body,
        );
        
        debugPrint('📊 Status: ${response.statusCode}');
        debugPrint('📊 Resposta: ${response.body}');
        
        if (response.statusCode == 201) {
          debugPrint('✅ Receita criada com sucesso!');
          
          // Verificar se a receita foi realmente salva
          final receitaCriada = json.decode(response.body);
          final idCriado = receitaCriada['id'];
          debugPrint('🆔 ID da receita criada: $idCriado');
          
          // Buscar a receita pelo ID para confirmar
          await Future.delayed(Duration(seconds: 1));
          final getResponse = await http.get(
            Uri.parse('$baseUrl/receitas/$idCriado'),
            headers: {'Accept': 'application/json'},
          );
          debugPrint('🔍 Verificação - Status: ${getResponse.statusCode}');
          debugPrint('🔍 Verificação - Resposta: ${getResponse.body}');
        } else {
          debugPrint('❌ Falha ao criar receita');
        }
      } catch (e) {
        debugPrint('❌ Erro durante teste de criação: $e');
      }
      
      debugPrint('🏁 FIM DOS TESTES DETALHADOS');
    } catch (e) {
      debugPrint('❌ Erro geral no teste detalhado: $e');
    }
  }
}
