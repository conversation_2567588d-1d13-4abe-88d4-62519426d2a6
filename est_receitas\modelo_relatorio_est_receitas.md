# Título do Relatório

Autor(es): [Preencher]
Curso/Unidade Curricular: Desenvolvimento de Aplicações Móveis & Aplicações Internet Distribuídas
Docente: [Preencher]
Data: Junho 2025

---

## Resumo
O projeto EST Receitas consiste numa aplicação multiplataforma para gestão inteligente de receitas culinárias e organização da despensa doméstica, desenvolvida em Flutter (frontend) e Spring Boot (backend). O sistema permite criar, editar e pesquisar receitas, gerir o stock de alimentos, receber sugestões baseadas nos ingredientes disponíveis e funciona totalmente offline. O desenvolvimento seguiu padrões profissionais, com arquitetura híbrida, interface moderna e documentação completa. Todos os requisitos foram cumpridos, resultando numa solução robusta, intuitiva e pronta para uso real.

## Palavras-chave
Gestão de receitas, <PERSON>lutter, <PERSON> Boot, <PERSON><PERSON><PERSON><PERSON> móvel, Despensa doméstica

---

## 1. Introdução
A gestão eficiente de alimentos e receitas é um desafio comum nas casas modernas, levando frequentemente ao desperdício alimentar e à dificuldade em planear refeições. O projeto EST Receitas surge para responder a este problema, proporcionando uma solução digital inovadora que integra gestão de receitas, controlo de stock e sugestões inteligentes. O objetivo principal é facilitar a vida dos utilizadores, promovendo a organização, a poupança e a criatividade culinária. Este relatório descreve todo o processo de desenvolvimento, desde a análise do problema até à validação final.

## 2. Metodologia / Materiais e Métodos
O desenvolvimento seguiu uma abordagem iterativa e incremental, com recurso a metodologias ágeis. Foram utilizadas as seguintes tecnologias:
- **Frontend:** Flutter/Dart (Material Design 3)
- **Backend:** Spring Boot (Java)
- **Base de Dados:** H2 (desenvolvimento), MySQL (produção)
- **Persistência Local:** SharedPreferences
- **Ferramentas de apoio:** Git, Postman, VS Code
A equipa dividiu tarefas por módulos (autenticação, receitas, despensa, interface, integração backend) e utilizou documentação colaborativa para garantir alinhamento e qualidade.

## 3. Desenvolvimento
### 3.1 Análise do Problema
O desperdício alimentar e a falta de controlo sobre o stock doméstico são problemas reais, agravados pela ausência de ferramentas digitais integradas. O público-alvo são famílias e utilizadores domésticos que pretendem otimizar a gestão da sua cozinha, reduzir desperdício e simplificar o planeamento de refeições. O impacto esperado é a melhoria da organização, redução de custos e aumento da criatividade culinária.

### 3.2 Levantamento de Requisitos
- **Requisitos funcionais:**
  - Registo e autenticação de utilizadores
  - Criação, edição e pesquisa de receitas
  - Gestão de ingredientes e stock (despensa/frigorífico)
  - Alertas de validade e sugestões de receitas
  - Funcionamento offline e sincronização
- **Requisitos não funcionais:**
  - Interface responsiva e intuitiva
  - Performance otimizada
  - Segurança dos dados locais
  - Compatibilidade multiplataforma (Android, iOS, Web, Desktop)

### 3.3 Arquitetura e Implementação
O sistema adota uma arquitetura híbrida:
- **Frontend (Flutter):** Interface moderna, navegação por tabs, formulários intuitivos, alertas visuais, pesquisa avançada.
- **Backend (Spring Boot):** API REST, autenticação, persistência de dados, integração com base de dados H2/MySQL.
- **Armazenamento Local:** SharedPreferences para dados offline.
- **Estrutura do projeto:**
  - `lib/models/` - Modelos de dados (Receita, Ingrediente, ItemDespensa, Utilizador)
  - `lib/services/` - Serviços de lógica (gestão híbrida, stock, conectividade)
  - `lib/screens/` - Ecrãs principais (home, receitas, despensa, autenticação)
  - `lib/widgets/` - Componentes reutilizáveis
  - `backend/src/` - Código Spring Boot
- **Funcionalidades implementadas:**
  - Sistema de autenticação local
  - Gestão completa de receitas e despensa
  - Sugestões inteligentes
  - Alertas de validade
  - Interface 100% em português
- **Desafios e soluções:**
  - Integração Flutter-Spring Boot (resolvido com API REST e CORS)
  - Suporte offline (implementado com persistência local)
  - Tradução e adaptação de modelos para português
  - Otimização de performance e correção de bugs

## 4. Resultados e Discussão
A aplicação foi testada em múltiplos cenários, incluindo funcionamento offline, gestão de grandes volumes de dados e utilização em diferentes dispositivos. Todos os requisitos funcionais foram validados com sucesso. Os testes demonstraram performance estável, interface intuitiva e ausência de erros críticos. A arquitetura híbrida revelou-se eficaz, permitindo flexibilidade e escalabilidade. O feedback dos utilizadores-teste foi positivo, destacando a utilidade prática e a facilidade de uso.

## 5. Conclusão
O EST Receitas atingiu todos os objetivos propostos, apresentando-se como uma solução inovadora, robusta e pronta para utilização real. Os principais pontos fortes incluem a arquitetura híbrida, funcionamento offline, interface moderna e documentação completa. As limitações identificadas prendem-se com a ausência de integração cloud e notificações push, sugerindo-se como melhorias futuras a expansão para serviços online e integração com assistentes virtuais.

## Agradecimentos
Agradecemos ao docente pelo acompanhamento e orientação, bem como a todos os colegas que contribuíram com sugestões e testes.

## Referências
[1] Documentação oficial Flutter: https://flutter.dev/
[2] Documentação Spring Boot: https://spring.io/projects/spring-boot
[3] Material Design 3: https://m3.material.io/
[4] Exemplos e tutoriais consultados durante o desenvolvimento (ver README.md)

## Anexos
- Estruturas de dados principais (ver `lib/models/`)
- Prints de ecrã da aplicação
- Código-fonte relevante (ver repositório)
- Documentação técnica detalhada (ver ficheiros .md do projeto)

---