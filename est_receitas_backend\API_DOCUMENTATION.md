# Est Receitas Backend - Documentação da API

## Base URL
```
http://localhost:8080/api
```

## Autenticação
A API utiliza JWT (JSON Web Tokens) para autenticação. Após o login, inclua o token no header:
```
Authorization: Bearer <token>
```

## Endpoints Disponíveis

### 🔐 Autenticação (`/auth`)

#### Registar Utilizador
- **POST** `/auth/registo`
- **Descrição**: Registar novo utilizador
- **Body**:
```json
{
  "nome": "<PERSON> Silva",
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456"
}
```
- **Resposta**: `201 Created` com token JWT

#### Login
- **POST** `/auth/login`
- **Descrição**: Autenticar utilizador
- **Body**:
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```
- **Resposta**: `200 OK` com token JWT

#### Verificar Token
- **POST** `/auth/verify`
- **Descrição**: Verificar se token é válido
- **Headers**: `Authorization: Bearer <token>`
- **Resposta**: `200 OK` com dados do utilizador

#### Logout
- **POST** `/auth/logout`
- **Descrição**: Terminar sessão
- **Headers**: `Authorization: Bearer <token>`
- **Resposta**: `200 OK`

#### Obter Perfil
- **GET** `/auth/profile`
- **Descrição**: Obter dados do perfil do utilizador
- **Headers**: `Authorization: Bearer <token>`
- **Resposta**: `200 OK` com dados do perfil

#### Atualizar Perfil
- **PUT** `/auth/profile`
- **Descrição**: Atualizar dados do perfil
- **Headers**: `Authorization: Bearer <token>`
- **Body**:
```json
{
  "nome": "João Silva Santos",
  "email": "<EMAIL>"
}
```
- **Resposta**: `200 OK` com dados atualizados

#### Verificar Email
- **GET** `/auth/check-email?email=<EMAIL>`
- **Descrição**: Verificar se email já está em uso
- **Resposta**: `200 OK` com boolean

#### Estatísticas de Utilizadores
- **GET** `/auth/stats`
- **Descrição**: Obter estatísticas de utilizadores
- **Headers**: `Authorization: Bearer <token>`
- **Resposta**: `200 OK` com estatísticas

### 🍽️ Receitas (`/receitas`)

#### Operações CRUD
- `GET /receitas` - Listar todas as receitas
- `GET /receitas/{id}` - Obter receita por ID
- `POST /receitas` - Criar nova receita
- `PUT /receitas/{id}` - Atualizar receita
- `DELETE /receitas/{id}` - Eliminar receita

#### Pesquisa e Filtros
- `GET /receitas/pesquisar?titulo={titulo}` - Pesquisar por título
- `GET /receitas/categoria/{categoria}` - Filtrar por categoria
- `GET /receitas/dificuldade/{dificuldade}` - Filtrar por dificuldade (FACIL, MEDIO, DIFICIL)
- `GET /receitas/tempo-maximo/{tempo}` - Filtrar por tempo máximo (minutos)
- `GET /receitas/ingrediente?ingrediente={nome}` - Pesquisar por ingrediente
- `GET /receitas/pesquisa-avancada` - Pesquisa com múltiplos critérios

#### Sugestões
- `GET /receitas/sugestoes` - Sugestões baseadas no stock disponível
- `POST /receitas/sugestoes-ingredientes` - Sugestões com ingredientes específicos

#### Auxiliares
- `GET /receitas/categorias` - Listar todas as categorias
- `GET /receitas/recentes` - Receitas mais recentes
- `GET /receitas/estatisticas/total` - Contar total de receitas
- `GET /receitas/estatisticas/por-categoria` - Estatísticas por categoria

### 📦 Stock (`/stock`)

#### Operações CRUD
- `GET /stock` - Listar todos os itens
- `GET /stock/{id}` - Obter item por ID
- `POST /stock` - Adicionar novo item
- `PUT /stock/{id}` - Atualizar item
- `DELETE /stock/{id}` - Remover item
- `PATCH /stock/{id}/quantidade?quantidade={valor}` - Atualizar apenas quantidade
- `PATCH /stock/{id}/validade?dataValidade={data}` - Atualizar apenas validade

#### Filtros por Localização
- `GET /stock/despensa` - Itens da despensa
- `GET /stock/frigorifico` - Itens do frigorífico
- `GET /stock/localizacao/{localizacao}` - Filtrar por localização (DESPENSA, FRIGORIFICO)

#### Pesquisa e Filtros
- `GET /stock/pesquisar?nome={nome}` - Pesquisar por nome
- `GET /stock/categoria/{categoria}` - Filtrar por categoria
- `GET /stock/marca/{marca}` - Filtrar por marca
- `GET /stock/pesquisa-avancada` - Pesquisa com múltiplos critérios

#### Gestão de Validades
- `GET /stock/vencidos` - Itens vencidos
- `GET /stock/proximos-vencimento?dias={dias}` - Itens próximos do vencimento (padrão: 7 dias)
- `GET /stock/vencem-entre?dataInicio={data}&dataFim={data}` - Itens que vencem num período
- `GET /stock/sem-validade` - Itens sem data de validade

#### Gestão de Estoque
- `GET /stock/estoque-baixo?limite={valor}` - Itens com estoque baixo (padrão: 5.0)

#### Auxiliares
- `GET /stock/categorias` - Listar todas as categorias
- `GET /stock/marcas` - Listar todas as marcas
- `GET /stock/unidades` - Listar todas as unidades
- `GET /stock/recentes` - Itens adicionados recentemente
- `GET /stock/estatisticas/total` - Contar total de itens
- `GET /stock/estatisticas/por-localizacao` - Estatísticas por localização
- `GET /stock/estatisticas/por-categoria` - Estatísticas por categoria
- `GET /stock/valor-total` - Valor total do stock

### 🧪 Teste (`/test`)
- `GET /test/health` - Verificar se a API está a funcionar
- `GET /test/cors` - Testar configuração CORS

## Modelos de Dados

### AuthResponse (Resposta de Autenticação)
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "type": "Bearer",
  "userId": 1,
  "nome": "João Silva",
  "email": "<EMAIL>",
  "role": "USER",
  "expiresAt": "2024-01-02T12:00:00Z",
  "success": true,
  "message": "Autenticação realizada com sucesso"
}
```

### UserProfile (Perfil de Utilizador)
```json
{
  "id": 1,
  "nome": "João Silva",
  "email": "<EMAIL>",
}
```

### LoginRequest (Pedido de Login)
```json
{
  "email": "<EMAIL>",
  "password": "123456"
}
```

### RegistoRequest (Pedido de Registo)
```json
{
  "nome": "João Silva",
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456"
}
```

### ReceitaDTO
```json
{
  "id": 1,
  "titulo": "Lasanha de Carne",
  "descricao": "Uma deliciosa lasanha tradicional",
  "instrucoes": "1. Prepare o molho...",
  "tempoPreparo": 90,
  "numeroPorcoes": 8,
  "dificuldade": "MEDIO",
  "categoria": "Pratos Principais",
  "imagemUrl": "assets/img/lasanha.jpeg",
  "ingredientes": [
    {
      "id": 1,
      "nome": "Massa de lasanha",
      "quantidade": 500,
      "unidade": "g",
      "observacoes": null,
      "receitaId": 1
    }
  ]
}
```

### StockItemDTO
```json
{
  "id": 1,
  "nome": "Arroz",
  "quantidade": 2,
  "unidade": "kg",
  "dataValidade": "2024-12-31",
  "localizacao": "DESPENSA",
  "categoria": "Cereais",
  "marca": "Tio João",
  "precoUnitario": 3.50,
}
```

### IngredienteDTO
```json
{
  "id": 1,
  "nome": "Massa de lasanha",
  "quantidade": 500,
  "unidade": "g",
  "observacoes": null,
  "receitaId": 1
}
```

## Enums

### DificuldadeReceita
- `FACIL`
- `MEDIO`
- `DIFICIL`

### TipoArmazenamento
- `DESPENSA`
- `FRIGORIFICO`

## Códigos de Resposta HTTP

- `200 OK` - Sucesso
- `201 Created` - Recurso criado com sucesso
- `204 No Content` - Operação bem-sucedida sem conteúdo
- `400 Bad Request` - Dados inválidos
- `404 Not Found` - Recurso não encontrado
- `409 Conflict` - Conflito (recurso duplicado)
- `422 Unprocessable Entity` - Erro de validação
- `500 Internal Server Error` - Erro interno do servidor

## Exemplos de Uso

### Criar uma receita
```bash
POST /api/receitas
Content-Type: application/json

{
  "titulo": "Omelete Simples",
  "descricao": "Omelete rápida e fácil",
  "instrucoes": "1. Bata os ovos. 2. Aqueça a frigideira. 3. Cozinhe por 3 minutos.",
  "tempoPreparo": 10,
  "numeroPorcoes": 2,
  "dificuldade": "FACIL",
  "categoria": "Pequeno-almoço",
  "ingredientes": [
    {
      "nome": "Ovos",
      "quantidade": 3,
      "unidade": "unidades"
    },
    {
      "nome": "Sal",
      "quantidade": 1,
      "unidade": "pitada"
    }
  ]
}
```

### Adicionar item ao stock
```bash
POST /api/stock
Content-Type: application/json

{
  "nome": "Leite",
  "quantidade": 1,
  "unidade": "litro",
  "dataValidade": "2024-08-25",
  "localizacao": "FRIGORIFICO",
  "categoria": "Lacticínios",
  "marca": "Mimosa",
  "precoUnitario": 1.30
}
```

## Configuração CORS

A API está configurada para aceitar requests de qualquer origem durante o desenvolvimento. Para produção, configure as origens específicas no ficheiro `CorsConfig.java`.
