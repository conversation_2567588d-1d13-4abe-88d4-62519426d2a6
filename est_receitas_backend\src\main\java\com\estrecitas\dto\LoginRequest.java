package com.estrecitas.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO para pedido de login
 */
public class LoginRequest {
    
    @NotBlank(message = "O email é obrigatório")
    @Email(message = "O email deve ter formato válido")
    private String email;
    
    @NotBlank(message = "A password é obrigatória")
    @Size(min = 6, message = "A password deve ter pelo menos 6 caracteres")
    private String password;
    
    // Construtores
    public LoginRequest() {}
    
    public LoginRequest(String email, String password) {
        this.email = email;
        this.password = password;
    }
    
    // Getters e Setters
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    @Override
    public String toString() {
        return "LoginRequest{" +
                "email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                '}';
    }
}
