com\estrecitas\controller\ReceitaController.class
com\estrecitas\controller\TestController.class
com\estrecitas\exception\DuplicateResourceException.class
com\estrecitas\model\Receita.class
com\estrecitas\dto\IngredienteDTO.class
com\estrecitas\exception\ResourceNotFoundException.class
com\estrecitas\service\UtilizadorService.class
com\estrecitas\exception\GlobalExceptionHandler.class
com\estrecitas\dto\LoginRequest.class
com\estrecitas\repository\IngredienteRepository.class
com\estrecitas\mapper\DTOMapper.class
com\estrecitas\dto\ReceitaDTO.class
com\estrecitas\model\StockItem.class
com\estrecitas\exception\ValidationException.class
com\estrecitas\dto\PerfilUtilizador.class
com\estrecitas\repository\StockItemRepository.class
com\estrecitas\model\Utilizador.class
com\estrecitas\config\SecurityConfig.class
com\estrecitas\dto\RegistoRequest.class
com\estrecitas\dto\StockItemDTO.class
com\estrecitas\dto\AuthResponse.class
com\estrecitas\dto\AtualizarPerfilRequest.class
com\estrecitas\service\ReceitaService.class
com\estrecitas\controller\AuthController.class
com\estrecitas\EstReceitasBackendApplication.class
com\estrecitas\repository\ReceitaRepository.class
com\estrecitas\dto\UtilizadorStatsResponse.class
com\estrecitas\controller\StockController.class
com\estrecitas\service\JwtService.class
com\estrecitas\repository\UtilizadorRepository.class
com\estrecitas\model\Ingrediente.class
com\estrecitas\service\StockService.class
com\estrecitas\model\DificuldadeReceita.class
com\estrecitas\model\TipoArmazenamento.class
