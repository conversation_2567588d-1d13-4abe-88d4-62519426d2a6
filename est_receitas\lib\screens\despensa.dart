import 'package:flutter/material.dart';
import '../models/item_despensa.dart';
import '../servicos/hibrido_stock_servico.dart';

class DespensaScreen extends StatefulWidget {
  const DespensaScreen({super.key});

  @override
  State<DespensaScreen> createState() => _DespensaScreenState();
}

class _DespensaScreenState extends State<DespensaScreen> {
  final HibridoStockServico _stockService = HibridoStockServico();
  List<ItemDespensa> _itensDespensa = [];
  bool _isLoading = true;



  @override
  void initState() {
    super.initState();
    _carregarItensDespensa();
  }

  Future<void> _carregarItensDespensa() async {
    try {
      final items = await _stockService.obterItensPorLocalizacao(LocalizacaoItem.despensa);
      setState(() {
        _itensDespensa = items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao carregar itens: $e')),
        );
      }
    }
  }

  Future<void> _deleteItem(int id) async {
    try {
      await _stockService.eliminarItem(id);
      _carregarItensDespensa(); // Recarregar lista
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Item removido com sucesso')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao remover item: $e')),
        );
      }
    }
  }

  void _showAddItemDialog() {
    final nomeController = TextEditingController();
    final quantidadeController = TextEditingController();
    final unidadeController = TextEditingController();
    DateTime? selectedDate;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Adicionar Item à Despensa'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nomeController,
                decoration: const InputDecoration(labelText: 'Nome do item'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantidadeController,
                decoration: const InputDecoration(labelText: 'Quantidade'),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: unidadeController,
                decoration: const InputDecoration(labelText: 'Unidade (kg, L, unidades, etc.)'),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Data de validade: '),
                  TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now().add(const Duration(days: 30)),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setDialogState(() {
                          selectedDate = date;
                        });
                      }
                    },
                    child: Text(
                      selectedDate != null
                          ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                          : 'Selecionar',
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nomeController.text.isNotEmpty &&
                    quantidadeController.text.isNotEmpty &&
                    unidadeController.text.isNotEmpty) {

                  // Guardar referências do context antes de operações assíncronas
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  try {
                    // Validar quantidade
                    final quantidade = double.tryParse(quantidadeController.text);
                    if (quantidade == null || quantidade <= 0) {
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(content: Text('Por favor, insira uma quantidade válida')),
                      );
                      return;
                    }

                    final item = ItemDespensa(
                      nome: nomeController.text.trim(),
                      quantidade: quantidade,
                      unidade: unidadeController.text.trim(),
                      localizacao: LocalizacaoItem.despensa,
                      dataValidade: selectedDate,
                    );

                    await _stockService.adicionarItem(item);

                    if (mounted) {
                      navigator.pop();
                      await _carregarItensDespensa();
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(content: Text('Item adicionado com sucesso')),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(content: Text('Erro ao adicionar item: $e')),
                      );
                    }
                  }
                }
              },
              child: const Text('Adicionar'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(ItemDespensa item) {
    return Card(
      child: ListTile(
        title: Text(item.nome),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Quantidade: ${item.quantidade} ${item.unidade}'),
            if (item.dataValidade != null)
              Text(
                'Validade: ${item.dataValidade!.day}/${item.dataValidade!.month}/${item.dataValidade!.year}',
                style: TextStyle(
                  color: item.estaVencido
                      ? Colors.red
                      : item.estaProximoDoVencimento()
                          ? Colors.orange
                          : Colors.grey[600],
                ),
              ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.delete, color: Colors.red),
          onPressed: () => _deleteItem(item.id!),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    // Ordenar itens por validade
    _itensDespensa.sort((a, b) {
      if (a.dataValidade == null && b.dataValidade == null) return 0;
      if (a.dataValidade == null) return 1;
      if (b.dataValidade == null) return -1;

      if (a.estaVencido && !b.estaVencido) return -1;
      if (!a.estaVencido && b.estaVencido) return 1;
      if (a.estaProximoDoVencimento() && !b.estaProximoDoVencimento()) return -1;
      if (!a.estaProximoDoVencimento() && b.estaProximoDoVencimento()) return 1;

      return a.dataValidade!.compareTo(b.dataValidade!);
    });

    return Scaffold(
      body: _itensDespensa.isEmpty
          ? const Center(
              child: Text(
                'Nenhum item na despensa.\nToque no + para adicionar.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _itensDespensa.length,
              itemBuilder: (context, index) {
                return _buildItemCard(_itensDespensa[index]);
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddItemDialog,
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}
