# Implementação de Serviços Híbridos

## Visão Geral

Para resolver o problema de integração entre o aplicativo Flutter e o backend, implementamos uma abordagem híbrida nos serviços da aplicação. Esta abordagem garante que:

1. Os dados são armazenados localmente para acesso offline
2. Quando há conectividade, os dados são sincronizados com o backend
3. Os serviços tentam primeiro operações no backend e, em caso de falha, usam armazenamento local como fallback

## Serviços Implementados

### 1. HybridReceitaService

Serviço de receitas que sincroniza dados entre armazenamento local e backend.

- **Operações implementadas**:
  - Obter todas as receitas
  - Obter receita por ID
  - Criar nova receita
  - Atualizar receita
  - Eliminar receita

- **Lógica de sincronização**:
  - Ao criar/atualizar: Tenta primeiro no backend, depois guarda localmente
  - Ao obter: Tenta primeiro no backend, usa local se falhar
  - Ao eliminar: Tenta eliminar no backend, sempre elimina localmente

### 2. HybridStockService

Serviço de gestão de estoque (despensa/frigorífico) com integração ao backend.

- **Operações implementadas**:
  - Obter todos os itens
  - Obter item por ID
  - Adicionar item
  - Atualizar item
  - Eliminar item
  - Obter itens por localização (despensa/frigorífico)

- **Lógica de sincronização**:
  - Utiliza a mesma abordagem do serviço de receitas
  - Operações específicas por localização também tentam usar endpoints dedicados do backend

### 3. HybridUtilizadorService

Serviço de gestão de utilizadores com integração parcial ao backend.

- **Operações implementadas**:
  - Registar utilizador
  - Login
  - Logout
  - Atualização de perfil

- **Limitações atuais**:
  - Integração limitada para atualizações de perfil devido à necessidade de autenticação por token

## Serviços de Apoio

### 1. ConnectivityService

Verifica o estado de conectividade para determinar se as operações podem ser feitas online.

### 2. SimpleStorageService

Gerencia operações de armazenamento local.

## Como Funciona

1. **Tentativa de operação online**:
   ```dart
   if (_connectivityService.isOnline) {
     try {
       // Tenta operação no backend primeiro
       final resultado = await _apiService.operacao(...);
       // Sucesso - salvar localmente também
     } catch (e) {
       // Falha - continua para operação local
     }
   }
   ```

2. **Fallback para armazenamento local**:
   ```dart
   // Operação local (independente de falha no backend)
   final dadosLocais = await obterDadosLocais();
   // Processar dados localmente
   await salvarDadosLocais(resultado);
   ```

## Benefícios

- **Resiliência**: O aplicativo funciona mesmo sem conectividade
- **Experiência do usuário**: Resposta rápida mesmo em conexões lentas
- **Consistência**: Dados são eventualmente sincronizados quando há conectividade
- **Desempenho**: Armazenamento local oferece acesso rápido aos dados

## Próximos Passos

1. Implementar sincronização periódica automática
2. Adicionar resolução de conflitos para dados divergentes
3. Implementar filas de operações pendentes quando offline
4. Adicionar indicadores visuais de estado de sincronização
