package com.estrecitas.dto;

/**
 * DTO para resposta de estatísticas de utilizadores
 */
public class UtilizadorStatsResponse {
    
    private long utilizadoresAtivos;
    private long totalUtilizadores;
    private long utilizadoresRegistadosHoje;
    private long utilizadoresRegistadosEsteMes;
    
    // Construtores
    public UtilizadorStatsResponse() {}
    
    public UtilizadorStatsResponse(long utilizadoresAtivos) {
        this.utilizadoresAtivos = utilizadoresAtivos;
    }
    
    public UtilizadorStatsResponse(long utilizadoresAtivos, long totalUtilizadores, 
                        long utilizadoresRegistadosHoje, long utilizadoresRegistadosEsteMes) {
        this.utilizadoresAtivos = utilizadoresAtivos;
        this.totalUtilizadores = totalUtilizadores;
        this.utilizadoresRegistadosHoje = utilizadoresRegistadosHoje;
        this.utilizadoresRegistadosEsteMes = utilizadoresRegistadosEsteMes;
    }
    
    // Getters e Setters
    public long getUtilizadoresAtivos() {
        return utilizadoresAtivos;
    }
    
    public void setUtilizadoresAtivos(long utilizadoresAtivos) {
        this.utilizadoresAtivos = utilizadoresAtivos;
    }
    
    public long getTotalUtilizadores() {
        return totalUtilizadores;
    }
    
    public void setTotalUtilizadores(long totalUtilizadores) {
        this.totalUtilizadores = totalUtilizadores;
    }
    
    /**
     * Obtém o número de utilizadores registados no dia atual.
     *
     * @return O número de utilizadores registados hoje
     */
    public long getUtilizadoresRegistadosHoje() {
        return utilizadoresRegistadosHoje;
    }
    
    public void setUtilizadoresRegistadosHoje(long utilizadoresRegistadosHoje) {
        this.utilizadoresRegistadosHoje = utilizadoresRegistadosHoje;
    }
    
    public long getUtilizadoresRegistadosEsteMes() {
        return utilizadoresRegistadosEsteMes;
    }
    
    public void setUtilizadoresRegistadosEsteMes(long utilizadoresRegistadosEsteMes) {
        this.utilizadoresRegistadosEsteMes = utilizadoresRegistadosEsteMes;
    }
    
    /**
     * Método toString() para apresentar os dados estatísticos em formato texto.
     *
     * @return Uma representação em texto do objeto com todos os seus campos
     */
    @Override
    public String toString() {
        return "UtilizadorStatsResponse{" +
                "utilizadoresAtivos=" + utilizadoresAtivos +
                ", totalUtilizadores=" + totalUtilizadores +
                ", utilizadoresRegistadosHoje=" + utilizadoresRegistadosHoje +
                ", utilizadoresRegistadosEsteMes=" + utilizadoresRegistadosEsteMes +
                '}';
    }
}
