# Est Receitas Backend

Backend Spring Boot para a aplicação de gestão de receitas e despensa.

## Tecnologias Utilizadas

- **Spring Boot 3.2.0**
- **Spring Web** - Para criar APIs REST
- **Spring Data JPA** - Para persistência de dados
- **H2 Database** - Base de dados em memória para desenvolvimento
- **MySQL** - Base de dados para produção (opcional)
- **Spring Boot DevTools** - Para desenvolvimento
- **Spring Security** - Para segurança (placeholder)

## Configuração

### Base de Dados
- **Desenvolvimento**: H2 em memória
- **URL**: `jdbc:h2:mem:testdb`
- **Console H2**: http://localhost:8080/h2-console
- **Username**: `sa`
- **Password**: `password`

### CORS
Configurado para aceitar requests de qualquer origem durante o desenvolvimento.

### Porta
A aplicação executa na porta **8080**.

## Como Executar

```bash
# Navegar para a pasta do backend
cd est_receitas_backend

# Executar com Maven
mvn spring-boot:run

# Ou compilar e executar
mvn clean package
java -jar target/est-receitas-backend-0.0.1-SNAPSHOT.jar
```

## Endpoints de Teste

- **Health Check**: `GET /api/test/health`
- **CORS Test**: `GET /api/test/cors`

## Estrutura do Projeto

```
src/
├── main/
│   ├── java/
│   │   └── com/estrecitas/
│   │       ├── EstReceitasBackendApplication.java
│   │       ├── config/
│   │       │   └── CorsConfig.java
│   │       └── controller/
│   │           └── TestController.java
│   └── resources/
│       └── application.properties
└── test/
```

## Próximos Passos

1. Implementar modelos de domínio (Receita, Ingrediente, StockItem)
2. Criar repositórios JPA
3. Implementar serviços de negócio
4. Criar controladores REST
5. Adicionar dados de exemplo
