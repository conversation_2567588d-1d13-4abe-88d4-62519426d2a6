import 'package:flutter/material.dart';
import '../../models/utilizador.dart';
import '../../servicos/hibrido_utilizador_servico.dart';
import 'login.dart';

/// Tela de visualização e edição do perfil do utilizador
/// Permite ao utilizador ver e atualizar seus dados pessoais,
/// alterar a password e fazer logout do sistema
class PerfilScreen extends StatefulWidget {
  const PerfilScreen({super.key});

  @override
  State<PerfilScreen> createState() => _PerfilScreenState();
}

class _PerfilScreenState extends State<PerfilScreen> {
  // Serviço para gestão do utilizador
  final _utilizadorService = HibridoUtilizadorServico();
  // Chave global para validação do formulário
  final _formKey = GlobalKey<FormState>();
  // Controladores para os campos de texto
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _telefoneController = TextEditingController();

  // Indicador de carregamento de dados
  bool _isLoading = false;
  // Indicador se o utilizador está em modo de edição
  bool _isEditing = false;
  // Dados do utilizador atual que serão exibidos e alterados
  Utilizador? _utilizador;

  @override
  void initState() {
    super.initState();
    // Carrega os dados do utilizador ao inicializar a página
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _carregarUtilizador();
    });
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _emailController.dispose();
    _telefoneController.dispose();
    super.dispose();
  }

  Future<void> _carregarUtilizador() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Garantir que o serviço está inicializado
      await _utilizadorService.inicializar();

      _utilizador = _utilizadorService.utilizadorAtual;

      if (_utilizador != null) {
        _nomeController.text = _utilizador!.nome;
        _emailController.text = _utilizador!.email;
        _telefoneController.text = _utilizador!.telefone ?? '';
        debugPrint('✅ Utilizador carregado: ${_utilizador!.nome}');
      } else {
        debugPrint('❌ Nenhum utilizador encontrado');
        // Se não há utilizador, redirecionar para login
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
            (route) => false,
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Erro ao carregar utilizador: $e');
      if (mounted) {
        _mostrarErro('Erro ao carregar dados do utilizador');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _guardarPerfil() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final utilizadorAtualizado = _utilizador!.copyWith(
        nome: _nomeController.text.trim(),
        email: _emailController.text.trim(),
        telefone: _telefoneController.text.trim().isEmpty 
            ? null 
            : _telefoneController.text.trim(),
      );

      final resultado = await _utilizadorService.atualizarPerfil(utilizadorAtualizado);

      if (mounted) {
        if (resultado.sucesso) {
          setState(() {
            _utilizador = resultado.utilizador;
            _isEditing = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultado.mensagem ?? 'Perfil atualizado com sucesso'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          _mostrarErro(resultado.mensagem ?? 'Erro ao atualizar perfil');
        }
      }
    } catch (e) {
      if (mounted) {
        _mostrarErro('Erro ao atualizar perfil: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _mostrarDialogoAlterarPassword() async {
    final passwordAtualController = TextEditingController();
    final novaPasswordController = TextEditingController();
    final confirmarPasswordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Alterar Password'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: passwordAtualController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Password Atual',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Insira a password atual';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: novaPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Nova Password',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Insira uma nova password';
                  }
                  if (value.length < 6) {
                    return 'A password deve ter pelo menos 6 caracteres';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: confirmarPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Confirmar Nova Password',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value != novaPasswordController.text) {
                    return 'As passwords não coincidem';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.of(context).pop();
                await _alterarPassword(
                  passwordAtualController.text,
                  novaPasswordController.text,
                );
              }
            },
            child: const Text('Alterar'),
          ),
        ],
      ),
    );
  }

  Future<void> _alterarPassword(String passwordAtual, String novaPassword) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final resultado = await _utilizadorService.alterarPassword(
        passwordAtual: passwordAtual,
        novaPassword: novaPassword,
      );

      if (mounted) {
        if (resultado.sucesso) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultado.mensagem ?? 'Password alterada com sucesso'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          _mostrarErro(resultado.mensagem ?? 'Erro ao alterar password');
        }
      }
    } catch (e) {
      if (mounted) {
        _mostrarErro('Erro ao alterar password: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _fazerLogout() async {
    final confirmar = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmar Logout'),
        content: const Text('Tem a certeza que deseja sair?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Sair'),
          ),
        ],
      ),
    );

    if (confirmar == true) {
      await _utilizadorService.logout();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    }
  }

  void _mostrarErro(String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        appBar: null,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('A carregar perfil...'),
            ],
          ),
        ),
      );
    }

    if (_utilizador == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Perfil'),
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Erro ao carregar perfil',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'Não foi possível carregar os dados do utilizador',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Perfil'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _guardarPerfil,
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 24),

              // Informações do utilizador
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Informações Pessoais',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Nome
                      TextFormField(
                        controller: _nomeController,
                        enabled: _isEditing,
                        decoration: const InputDecoration(
                          labelText: 'Nome',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'O nome é obrigatório';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Email
                      TextFormField(
                        controller: _emailController,
                        enabled: _isEditing,
                        decoration: const InputDecoration(
                          labelText: 'Email',
                          prefixIcon: Icon(Icons.email),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'O email é obrigatório';
                          }
                          if (!value.contains('@')) {
                            return 'O email é inválido';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Telefone
                      TextFormField(
                        controller: _telefoneController,
                        enabled: _isEditing,
                        decoration: const InputDecoration(
                          labelText: 'Telefone',
                          prefixIcon: Icon(Icons.phone),
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Informações da conta
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Informações da Conta',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),                  
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Ações
              if (!_isEditing) ...[
                ElevatedButton.icon(
                  onPressed: _mostrarDialogoAlterarPassword,
                  icon: const Icon(Icons.lock),
                  label: const Text('Alterar a password'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
                const SizedBox(height: 16),

                ElevatedButton.icon(
                  onPressed: _fazerLogout,
                  icon: const Icon(Icons.logout),
                  label: const Text('Sair'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],

              if (_isLoading)
                const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
