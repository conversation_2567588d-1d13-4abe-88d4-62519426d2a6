{"info": {"name": "EST Receitas API Tests", "description": "Colecção completa para testar a API EST Receitas", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api"}, {"key": "authToken", "value": ""}], "item": [{"name": "🔐 Autenticação", "item": [{"name": "1. Verificar Saúde", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/testes/saude", "host": ["{{baseUrl}}"], "path": ["testes", "saude"]}}}, {"name": "2. Registar Utilizador", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('Token guardado:', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nome\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\",\n  \"confirmPassword\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/autenticacao/registo", "host": ["{{baseUrl}}"], "path": ["autenticacao", "registo"]}}}, {"name": "3. <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.collectionVariables.set('authToken', response.token);", "        console.log('Token atualizado:', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/autenticacao/login", "host": ["{{baseUrl}}"], "path": ["autenticacao", "login"]}}}, {"name": "4. Verificar <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/autenticacao/verificar", "host": ["{{baseUrl}}"], "path": ["autenticacao", "verificar"]}}}, {"name": "5. <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/autenticacao/perfil", "host": ["{{baseUrl}}"], "path": ["autenticacao", "perfil"]}}}, {"name": "6. <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"nome\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/autenticacao/perfil", "host": ["{{baseUrl}}"], "path": ["autenticacao", "perfil"]}}}, {"name": "7. <PERSON>eri<PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/autenticacao/verificar-email?email=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["autenticacao", "verificar-email"], "query": [{"key": "email", "value": "<EMAIL>"}]}}}, {"name": "8. E<PERSON>tís<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/autenticacao/estatisticas", "host": ["{{baseUrl}}"], "path": ["autenticacao", "estatisticas"]}}}, {"name": "9. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/autenticacao/logout", "host": ["{{baseUrl}}"], "path": ["autenticacao", "logout"]}}}]}, {"name": "🍽️ Receitas", "item": [{"name": "Listar Receitas", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/receitas", "host": ["{{baseUrl}}"], "path": ["receitas"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"titulo\": \"Omelete Simples\",\n  \"descricao\": \"Omelete rápida e fácil\",\n  \"instrucoes\": \"1. Bata os ovos. 2. Aqueça a frigideira. 3. Cozinhe por 3 minutos.\",\n  \"tempoPreparo\": 10,\n  \"numeroPorcoes\": 2,\n  \"dificuldade\": \"FACIL\",\n  \"categoria\": \"Pequeno-almoço\",\n  \"ingredientes\": [\n    {\n      \"nome\": \"Ovos\",\n      \"quantidade\": 3,\n      \"unidade\": \"unidades\"\n    },\n    {\n      \"nome\": \"Sal\",\n      \"quantidade\": 1,\n      \"unidade\": \"pitada\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/receitas", "host": ["{{baseUrl}}"], "path": ["receitas"]}}}]}, {"name": "📦 Stock", "item": [{"name": "Listar Stock", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stock", "host": ["{{baseUrl}}"], "path": ["stock"]}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nome\": \"Leite\",\n  \"quantidade\": 1,\n  \"unidade\": \"litro\",\n  \"dataValidade\": \"2024-08-25\",\n  \"localizacao\": \"FRIGORIFICO\",\n  \"categoria\": \"Lacticínios\",\n  \"marca\": \"Mimosa\",\n  \"precoUnitario\": 1.30\n}"}, "url": {"raw": "{{baseUrl}}/stock", "host": ["{{baseUrl}}"], "path": ["stock"]}}}]}]}