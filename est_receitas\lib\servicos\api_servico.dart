import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import '../models/receita.dart';
import '../models/item_despensa.dart';
import '../models/utilizador.dart';

/// Serviço para comunicação direta com o backend Spring Boot
class ApiService {
  
  // ===== MÉTODOS AUXILIARES =====
  
  /// Fazer requisição GET
  static Future<http.Response> _get(String url) async {
    try {
      debugPrint('🌐 GET: $url');
      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.connectTimeout);
      
      debugPrint('📡 Response: ${response.statusCode}');
      return response;
    } catch (e) {
      debugPrint('❌ Erro GET $url: $e');
      rethrow;
    }
  }
  
  /// Fazer requisição POST
  static Future<http.Response> _post(String url, Map<String, dynamic> data) async {
    try {
      debugPrint('🌐 POST: $url');
      debugPrint('📤 Data: ${jsonEncode(data)}');
      
      final response = await http.post(
        Uri.parse(url),
        headers: ApiConfig.jsonHeaders,
        body: jsonEncode(data),
      ).timeout(ApiConfig.connectTimeout);
      
      debugPrint('📡 Response: ${response.statusCode}');
      return response;
    } catch (e) {
      debugPrint('❌ Erro POST $url: $e');
      rethrow;
    }
  }
  
  /// Fazer requisição PUT
  static Future<http.Response> _put(String url, Map<String, dynamic> data) async {
    try {
      debugPrint('🌐 PUT: $url');
      final response = await http.put(
        Uri.parse(url),
        headers: ApiConfig.jsonHeaders,
        body: jsonEncode(data),
      ).timeout(ApiConfig.connectTimeout);
      
      debugPrint('📡 Response: ${response.statusCode}');
      return response;
    } catch (e) {
      debugPrint('❌ Erro PUT $url: $e');
      rethrow;
    }
  }
  
  /// Fazer requisição DELETE
  static Future<http.Response> _delete(String url) async {
    try {
      debugPrint('🌐 DELETE: $url');
      final response = await http.delete(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.connectTimeout);
      
      debugPrint('📡 Response: ${response.statusCode}');
      return response;
    } catch (e) {
      debugPrint('❌ Erro DELETE $url: $e');
      rethrow;
    }
  }
  
  // ===== RECEITAS =====
  
  /// Obter todas as receitas
  static Future<List<Receita>> obterTodasReceitas() async {
    try {
      final response = await _get(ApiConfig.receitasUrl);
      
      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList.map((json) => Receita.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao obter receitas: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao obter receitas: $e');
      // Retornar lista vazia em caso de erro
      return [];
    }
  }
  
  /// Criar nova receita
  static Future<Receita> criarReceita(Receita receita) async {
    try {
      final response = await _post(ApiConfig.receitasUrl, receita.toJson());
      
      if (response.statusCode == 201 || response.statusCode == 200) {
        final json = jsonDecode(response.body);
        return Receita.fromJson(json);
      } else {
        throw Exception('Erro ao criar receita: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao criar receita: $e');
      rethrow;
    }
  }
  
  /// Obter receita por ID
  static Future<Receita?> obterReceitaPorId(String id) async {
    try {
      final response = await _get('${ApiConfig.receitasUrl}/$id');
      
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        return Receita.fromJson(json);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Erro ao obter receita: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao obter receita por ID: $e');
      return null;
    }
  }
  
  /// Atualizar receita
  static Future<Receita> atualizarReceita(Receita receita) async {
    try {
      final response = await _put('${ApiConfig.receitasUrl}/${receita.id}', receita.toJson());
      
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        return Receita.fromJson(json);
      } else {
        throw Exception('Erro ao atualizar receita: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao atualizar receita: $e');
      rethrow;
    }
  }
  
  /// Eliminar receita
  static Future<bool> eliminarReceita(String id) async {
    try {
      final response = await _delete('${ApiConfig.receitasUrl}/$id');
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      debugPrint('❌ Erro ao eliminar receita: $e');
      return false;
    }
  }
  
  // ===== STOCK =====
  
  /// Obter todos os itens de stock
  static Future<List<ItemDespensa>> obterTodosItens() async {
    try {
      final response = await _get(ApiConfig.stockUrl);
      
      if (response.statusCode == 200) {
        final List<dynamic> jsonList = jsonDecode(response.body);
        return jsonList.map((json) => ItemDespensa.fromJson(json)).toList();
      } else {
        throw Exception('Erro ao obter itens: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao obter itens: $e');
      return [];
    }
  }
  
  /// Adicionar item ao stock
  static Future<ItemDespensa> adicionarItem(ItemDespensa item) async {
    try {
      final response = await _post(ApiConfig.stockUrl, item.toJson());
      
      if (response.statusCode == 201 || response.statusCode == 200) {
        final json = jsonDecode(response.body);
        return ItemDespensa.fromJson(json);
      } else {
        throw Exception('Erro ao adicionar item: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao adicionar item: $e');
      rethrow;
    }
  }
  
  /// Atualizar item do stock
  static Future<ItemDespensa> atualizarItem(ItemDespensa item) async {
    try {
      final response = await _put('${ApiConfig.stockUrl}/${item.id}', item.toJson());
      
      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        return ItemDespensa.fromJson(json);
      } else {
        throw Exception('Erro ao atualizar item: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('❌ Erro ao atualizar item: $e');
      rethrow;
    }
  }
  
  /// Eliminar item do stock
  static Future<bool> eliminarItem(int id) async {
    try {
      final response = await _delete('${ApiConfig.stockUrl}/$id');
      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      debugPrint('❌ Erro ao eliminar item: $e');
      return false;
    }
  }
  
  // ===== TESTE DE CONECTIVIDADE =====
  
  /// Verificar se o backend está disponível
  static Future<bool> verificarConectividade() async {
    try {
      final response = await _get('${ApiConfig.baseUrl}/test/health');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('❌ Backend não disponível: $e');
      return false;
    }
  }
}
