import 'package:flutter/material.dart';
import '../debug_api.dart';
import '../utils/constants.dart';

class ConfigScreen extends StatefulWidget {
  const ConfigScreen({super.key});

  @override
  State<ConfigScreen> createState() => _ConfigScreenState();
}

class _ConfigScreenState extends State<ConfigScreen> {
  bool _testingApi = false;
  String _testResult = '';

  Future<void> _runApiTest() async {
    setState(() {
      _testingApi = true;
      _testResult = 'Testando conexão com o backend...';
    });

    try {
      // Testar conectividade
      final conectado = await DebugApi.testarConectividade();
      
      if (!conectado) {
        setState(() {
          _testResult = 'Erro: Não foi possível conectar ao backend.';
          _testingApi = false;
        });
        return;
      }
      
      setState(() {
        _testResult = 'Conectado ao backend! A testar a criação de receita...';
      });
      
      // Testar criação de receita
      await Future.delayed(const Duration(seconds: 1));
      final resultado = await DebugApi.testarCriacaoReceita();
      
      if (resultado) {
        setState(() {
          _testResult = 'Sucesso! Receita criada no backend.';
          _testingApi = false;
        });
      } else {
        setState(() {
          _testResult = 'Erro: Falha ao criar receita no backend.';
          _testingApi = false;
        });
      }
    } catch (e) {
      setState(() {
        _testResult = 'Erro: $e';
        _testingApi = false;
      });
    }
  }

  Future<void> _runDetailedApiTest() async {
    setState(() {
      _testingApi = true;
      _testResult = 'A executar os testes detalhado da API...';
    });

    try {
      // Executar o teste detalhado e capturar o log
      await DebugApi.testarApiDetalhada();
      
      setState(() {
        _testResult = 'Teste detalhado concluído. Verifique os logs de debug para resultados completos.';
        _testingApi = false;
      });
    } catch (e) {
      setState(() {
        _testResult = 'Erro no teste detalhado: $e';
        _testingApi = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configurações'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Configurações da aplicação',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            const Text(
              'Backend API',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Testar comunicação com o backend',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Clique para verificar se a aplicação consegue comunicar com o servidor backend e criar receitas.',
                    ),
                    const SizedBox(height: 16),
                    
                    if (_testResult.isNotEmpty)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: _testResult.contains('✅') ? Colors.green.shade50 : Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _testResult.contains('✅') ? Colors.green : Colors.red,
                          ),
                        ),
                        child: Text(_testResult),
                      ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ElevatedButton(
                          onPressed: _testingApi ? null : _runApiTest,
                          child: const Text('Testar API Básico'),
                        ),
                        ElevatedButton(
                          onPressed: _testingApi ? null : _runDetailedApiTest,
                          child: const Text('Teste Detalhado'),
                        ),
                      ],
                    ),
                    
                    if (_testingApi)
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(child: CircularProgressIndicator()),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
