import 'package:flutter/material.dart';
import '../utils/constants.dart';

/// Tela de configurações da aplicação
/// Permite ao utilizador ajustar preferências e ver informações sobre a app
class ConfigScreen extends StatefulWidget {
  const ConfigScreen({super.key});

  @override
  State<ConfigScreen> createState() => _ConfigScreenState();
}

class _ConfigScreenState extends State<ConfigScreen> {
  /// Constrói a interface da tela de configurações
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // === BARRA DE NAVEGAÇÃO ===
      appBar: AppBar(
        title: const Text('Configurações'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),

      // === CORPO DA TELA ===
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Título principal
            const Text(
              'Configurações da aplicação',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),

            // === SEÇÃO DE PREFERÊNCIAS ===
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Preferências',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // TODO: Adicionar opções de configuração aqui
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // === SEÇÃO SOBRE A APLICAÇÃO ===
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Sobre',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Informações da aplicação
                    const Text('EST Receitas v1.0.0'),
                    const SizedBox(height: 8),
                    const Text('Aplicação para gestão de receitas e despensa'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
