package com.estrecitas.controller;

import com.estrecitas.dto.*;
import com.estrecitas.model.Utilizador;
import com.estrecitas.service.JwtService;
import com.estrecitas.service.UtilizadorService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * Controller para autenticação e gestão de utilizadores
 */
@RestController
@RequestMapping("/api/autenticacao")
@CrossOrigin(origins = "*")
public class AutenticacaoController {
    
    @Autowired
    private UtilizadorService utilizadorService;
    
    @Autowired
    private JwtService jwtService;

    /**Registar novo utilizador*/
    @PostMapping("/registo")
    public ResponseEntity<AuthResponse> registo(@Valid @RequestBody RegistoRequest request) {
        try {
            // Registar utilizador
            Utilizador utilizador = utilizadorService.registarUtilizador(request);

            // Gerar token
            String token = jwtService.generateToken(utilizador);

            // Criar resposta
            AuthResponse response = AuthResponse.success(
                token,
                utilizador.getId(),
                utilizador.getNome(),
                utilizador.getEmail()
            );
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (Exception e) {
            AuthResponse response = AuthResponse.error("Erro no registo: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * Login de utilizador
     */
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            // Autenticar utilizador
            Optional<Utilizador> utilizadorOpt = utilizadorService.authenticateUtilizador(request.getEmail(), request.getPassword());
            
            if (utilizadorOpt.isPresent()) {
                Utilizador utilizador = utilizadorOpt.get();

                // Gerar token
                String token = jwtService.generateToken(utilizador);

                // Criar resposta
                AuthResponse response = AuthResponse.success(
                    token,
                    utilizador.getId(), 
                    utilizador.getNome(), 
                    utilizador.getEmail()
                );
                
                return ResponseEntity.ok(response);
            } else {
                AuthResponse response = AuthResponse.error("Credenciais inválidas");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
        } catch (Exception e) {
            AuthResponse response = AuthResponse.error("Erro no login: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * Verificar token
     */
    @PostMapping("/verificar")
    public ResponseEntity<AuthResponse> verificarToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                AuthResponse response = AuthResponse.error("Token não fornecido");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String token = authHeader.substring(7);

            if (jwtService.validateToken(token)) {
                Long utilizadorId = jwtService.getUserIdFromToken(token);
                Optional<Utilizador> utilizadorOpt = utilizadorService.getUtilizadorById(utilizadorId);
                
                if (utilizadorOpt.isPresent()) {
                    Utilizador utilizador = utilizadorOpt.get();

                    AuthResponse response = AuthResponse.success(
                        token,
                        utilizador.getId(), 
                        utilizador.getNome(), 
                        utilizador.getEmail()
                    );
                    
                    return ResponseEntity.ok(response);
                } else {
                    AuthResponse response = AuthResponse.error("Utilizador não encontrado");
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
                }
            } else {
                AuthResponse response = AuthResponse.error("Token inválido ou expirado");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
        } catch (Exception e) {
            AuthResponse response = AuthResponse.error("Erro na verificação: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
    }
    
    /**
     * Logout (invalidar token - apenas resposta de sucesso)
     */
    @PostMapping("/logout")
    public ResponseEntity<AuthResponse> logout() {
        // Em implementação simples, apenas retornamos sucesso
        // Em implementação completa, adicionaríamos token a blacklist
        AuthResponse response = AuthResponse.success(null, null, null, null);
        response.setMessage("Logout realizado com sucesso");
        return ResponseEntity.ok(response);
    }
    
    // ===== ENDPOINTS DE PERFIL =====
    
    /**
     * Obter perfil do utilizador
     */
    @GetMapping("/perfil")
    public ResponseEntity<?> obterPerfil(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token não fornecido");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtService.validateToken(token)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token inválido");
            }

            Long utilizadorId = jwtService.getUserIdFromToken(token);
            Optional<PerfilUtilizador> profileOpt = utilizadorService.getUtilizadorPerfil(utilizadorId);

            if (profileOpt.isPresent()) {
                return ResponseEntity.ok(profileOpt.get());
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Utilizador não encontrado");
            }
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erro interno: " + e.getMessage());
        }
    }
    
    /**
     * Atualizar perfil do utilizador
     */
    @PutMapping("/perfil")
    public ResponseEntity<?> atualizarPerfil(
            @RequestHeader("Authorization") String authHeader,
            @Valid @RequestBody AtualizarPerfilRequest request) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token não fornecido");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtService.validateToken(token)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token inválido");
            }
            
            Long utilizadorId = jwtService.getUserIdFromToken(token);
            Utilizador utilizadorAtualizado = utilizadorService.atualizarUtilizadorPerfil(utilizadorId, request.getNome(), request.getEmail());
            
            return ResponseEntity.ok(PerfilUtilizador.fromUtilizador(utilizadorAtualizado));
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Erro na atualização: " + e.getMessage());
        }
    }
    
    /**
     * Verificar se email existe
     */
    @GetMapping("/verificar-email")
    public ResponseEntity<Boolean> verificarEmail(@RequestParam String email) {
        boolean exists = utilizadorService.emailExists(email);
        return ResponseEntity.ok(exists);
    }
    
    /**
     * Estatísticas de utilizadores (apenas para admins)
     */
    @GetMapping("/estatisticas")
    public ResponseEntity<?> obterEstatisticas(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token não fornecido");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtService.validateToken(token)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Token inválido");
            }
            
            long utilizadoresAtivos = utilizadorService.countActiveUtilizadores();

            return ResponseEntity.ok(new UtilizadorStatsResponse(utilizadoresAtivos));
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Erro interno: " + e.getMessage());
        }
    }

}
