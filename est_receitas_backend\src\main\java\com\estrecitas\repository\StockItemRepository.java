package com.estrecitas.repository;

import com.estrecitas.model.StockItem;
import com.estrecitas.model.TipoArmazenamento;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface StockItemRepository extends JpaRepository<StockItem, Long> {
    
    // Procurar itens por localização
    List<StockItem> findByLocalizacao(TipoArmazenamento localizacao);
    
    // Procurar itens da despensa
    default List<StockItem> findDespensaItems() {
        return findByLocalizacao(TipoArmazenamento.DESPENSA);
    }

    // Procurar itens do frigorífico
    default List<StockItem> findFrigorificoItems() {
        return findByLocalizacao(TipoArmazenamento.FRIGORIFICO);
    }
    
    // Procurar itens por nome
    List<StockItem> findByNomeContainingIgnoreCase(String nome);
    

    

    
    // Procurar itens por quantidade mínima
    List<StockItem> findByQuantidadeGreaterThanEqual(Double quantidadeMinima);
    
    // Procurar itens por quantidade máxima
    List<StockItem> findByQuantidadeLessThanEqual(Double quantidadeMaxima);
    

    

    

    
    // Procurar todas as unidades distintas
    @Query("SELECT DISTINCT s.unidade FROM StockItem s WHERE s.unidade IS NOT NULL ORDER BY s.unidade")
    List<String> findDistinctUnidades();
    

    
    // Procurar nomes de ingredientes disponíveis (para sugestões de receitas)
    @Query("SELECT DISTINCT LOWER(s.nome) FROM StockItem s WHERE s.quantidade > 0")
    List<String> findNomesIngredientesDisponiveis();
    
    // Verificar se existe item com nome específico
    boolean existsByNomeIgnoreCase(String nome);
    

}
