package com.estrecitas.exception;

/**
 * Exceção lançada quando um recurso solicitado não é encontrado no sistema.
 * Esta exceção é utilizada para sinalizar situações em que entidades como utilizadores,
 * receitas, ingredientes, etc. foram solicitados mas não existem na base de dados.
 */

public class ResourceNotFoundException extends RuntimeException {
    
    public ResourceNotFoundException(String message) {
        super(message);
    }
    
    public ResourceNotFoundException(String resourceName, String fieldName, Object fieldValue) {
        super(String.format("%s não encontrado com %s: '%s'", resourceName, fieldName, fieldValue));
    }
    
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
