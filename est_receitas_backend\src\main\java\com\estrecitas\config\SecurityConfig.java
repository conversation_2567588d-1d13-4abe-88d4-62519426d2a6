package com.estrecitas.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Configuração de segurança Spring Security para a aplicação EST Receitas
 *
 * Esta classe configura:
 * - Autenticação e autorização de endpoints
 * - Configuração CORS para integração com frontend Flutter
 * - Política de sessões stateless (para JWT)
 * - Encoder de passwords com BCrypt
 * - Permissões específicas para diferentes tipos de endpoints
 *
 * A configuração permite acesso público aos endpoints de autenticação e teste,
 * enquanto protege outros recursos que requerem autenticação.
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    /**
     * Configuração da cadeia de filtros de segurança HTTP
     *
     * Define as regras de autorização para diferentes endpoints:
     * - Endpoints públicos: /api/autenticacao/**, /api/testes/**, /api/receitas/**, /api/stock/**
     * - Endpoints protegidos: todos os outros requerem autenticação
     * - Desabilita CSRF (não necessário para APIs REST stateless)
     * - Configura CORS para permitir requests do frontend Flutter
     * - Define política de sessões como STATELESS (para JWT)
     *
     * @param http configuração de segurança HTTP
     * @return cadeia de filtros de segurança configurada
     * @throws Exception se houver erro na configuração
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // Desabilitar CSRF para APIs REST
            .csrf(csrf -> csrf.disable())
            
            // Configurar CORS
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            
            // Configurar autorização de requests
            .authorizeHttpRequests(authz -> authz
                // Permitir acesso público aos endpoints de autenticação
                .requestMatchers("/api/autenticacao/**").permitAll()

                // Permitir acesso público aos endpoints de teste
                .requestMatchers("/api/testes/**").permitAll()
                
                // Permitir acesso público aos endpoints de receitas e stock
                .requestMatchers("/api/receitas/**").permitAll()
                .requestMatchers("/api/stock/**").permitAll()
                
                // Permitir acesso ao console H2 (apenas para desenvolvimento)
                .requestMatchers("/h2-console/**").permitAll()
                
                // Permitir acesso a recursos estáticos
                .requestMatchers("/static/**", "/public/**").permitAll()
                
                // Todos os outros endpoints requerem autenticação
                .anyRequest().authenticated()
            )
            
            // Configurar gestão de sessões (stateless para JWT)
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            )
            
            // Desabilitar frame options para H2 console
            .headers(headers -> headers
                .frameOptions(frameOptions -> frameOptions.disable())
            );
        
        return http.build();
    }
    
    /**
     * Configuração CORS (Cross-Origin Resource Sharing) para permitir requests do frontend Flutter
     *
     * Configura as políticas CORS para permitir que o frontend Flutter (executando em localhost)
     * faça requests para o backend. Inclui:
     * - Origens permitidas: localhost e 127.0.0.1 em qualquer porta
     * - Métodos HTTP permitidos: GET, POST, PUT, DELETE, OPTIONS, PATCH
     * - Headers permitidos: Authorization, Content-Type, Accept, etc.
     * - Credenciais permitidas para autenticação
     * - Headers expostos para o frontend
     *
     * @return fonte de configuração CORS
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Permitir origens específicas (adicionar URLs do frontend)
        configuration.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:*",
            "http://127.0.0.1:*",
            "https://localhost:*",
            "https://127.0.0.1:*"
        ));
        
        // Permitir métodos HTTP
        configuration.setAllowedMethods(Arrays.asList(
            "GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"
        ));
        
        // Permitir headers
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers"
        ));
        
        // Permitir credenciais
        configuration.setAllowCredentials(true);
        
        // Expor headers
        configuration.setExposedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type"
        ));
        
        // Aplicar configuração a todos os endpoints
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        return source;
    }
    
    /**
     * Bean para encoder de passwords usando BCrypt
     *
     * BCrypt é um algoritmo de hash de password seguro que inclui:
     * - Salt automático para prevenir ataques de rainbow table
     * - Custo configurável para ajustar a complexidade computacional
     * - Resistência a ataques de força bruta
     *
     * Usado para:
     * - Hash de passwords durante o registo de utilizadores
     * - Verificação de passwords durante o login
     *
     * @return encoder BCrypt para passwords
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
