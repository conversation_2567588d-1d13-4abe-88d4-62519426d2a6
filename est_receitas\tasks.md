# 📱 Aplicação Móvel: Receitas e Gestão de Despensa

## 🎯 Objetivo
Desenvolver uma aplicação móvel que permita:
- Criar e visualizar receitas.
- Guardar e gerir os alimentos disponíveis na despensa e frigorífico.
- Sugerir receitas com base nos ingredientes disponíveis.

---

## 🗂️ Estrutura de Pastas

📦 ReceitasApp
├── assets/
├── lib/
│ ├── main.dart
│ ├── screens/
│ │ ├── home_screen.dart
│ │ ├── recipe_list.dart
│ │ ├── recipe_detail.dart
│ │ ├── pantry_screen.dart
│ │ ├── fridge_screen.dart
│ │ ├── create_recipe.dart
│ ├── models/
│ │ ├── recipe.dart
│ │ ├── ingredient.dart
│ │ ├── pantry_item.dart
│ ├── services/
│ │ ├── recipe_service.dart
│ │ ├── pantry_service.dart
│ │ ├── storage_service.dart
│ ├── widgets/
│ │ ├── recipe_card.dart
│ │ ├── ingredient_chip.dart
│ │ ├── pantry_item_tile.dart
│ │ ├── add_ingredient_form.dart
│ │ ├── empty_state.dart
│ └── utils/
│ ├── constants.dart
│ ├── helpers.dart

yaml
---

## 📋 Tarefas e Subtarefas

### 1. Configuração Inicial
- [x] Inicializar projeto Flutter.
- [x] Definir dependências no `pubspec.yaml`. (básicas definidas)
- [x] Criar estrutura de pastas inicial. (parcial - falta organização completa)

---

### 2. Modelo de Dados
#### 2.1 Receita
- [x] Criar `Recipe` com:
  - id
  - título
  - descrição
  - lista de ingredientes
  - modo de preparação

#### 2.2 Ingrediente
- [x] Criar `Ingredient` com:
  - nome
  - quantidade
  - unidade

#### 2.3 Item de Despensa/Frigorífico
- [x] Criar `PantryItem` com:
  - nome
  - quantidade
  - validade (opcional)
  - localização (despensa ou frigorífico)

---

### 3. Gestão de Receitas
#### 3.1 Listar Receitas
- [x] Criar ecrã para mostrar lista de receitas (`recipe_list.dart`). (implementado em `favorites.dart`)
- [x] Mostrar título e imagem (mock). (implementado com dados estáticos)
- [x] Permitir clicar para ver detalhes.

#### 3.2 Detalhes da Receita
- [x] Criar `recipe_detail.dart`.
- [x] Mostrar título, ingredientes e preparação.

#### 3.3 Criar Receita
- [x] Criar formulário para nova receita (`create_recipe.dart`). (implementado em `novaReceita.dart`)
- [x] Adicionar ingredientes dinamicamente.
- [ ] Guardar dados localmente.

---

### 4. Gestão de Alimentos
#### 4.1 Ecrã de Despensa
- [x] Listar todos os alimentos marcados como "despensa".
- [x] Permitir adicionar, editar ou remover.

#### 4.2 Ecrã de Frigorífico
- [x] Igual ao anterior mas para "frigorífico".

#### 4.3 Adicionar Alimento
- [x] Criar componente `add_ingredient_form.dart`.
- [x] Definir nome, quantidade, validade e localização.

---

### 5. Lógica de Sugestão de Receitas
- [x] Verificar ingredientes disponíveis.
- [x] Sugerir receitas que podem ser feitas com os ingredientes disponíveis.
- [x] Mostrar receitas com destaque se todos os ingredientes estiverem disponíveis.

---

### 6. Serviços (Lógica)
#### 6.1 RecipeService
- [x] CRUD para receitas.
- [x] Guardar e carregar localmente (uso de `shared_preferences` ou `hive`).

#### 6.2 PantryService
- [x] CRUD para itens de despensa/frigorífico.
- [x] Atualização automática ao adicionar/remover.

#### 6.3 StorageService
- [x] Guardar dados no armazenamento local.
- [x] Abstração para facilitar troca futura por API.

---

### 7. Componentes Reutilizáveis
#### 7.1 recipe_card.dart
- [ ] Componente compacto com imagem e título.

#### 7.2 ingredient_chip.dart
- [ ] Chip para representar um ingrediente.

#### 7.3 pantry_item_tile.dart
- [ ] Tile para mostrar um item da despensa/frigorífico.

#### 7.4 empty_state.dart
- [ ] Componente visual para estados vazios.

---

### 8. UI e Navegação
- [x] Implementar `BottomNavigationBar` para:
  - [x] Home (implementado mas não funcional)
  - [x] Receitas (implementado)
  - [x] Despensa (implementado mas não funcional)
  - [ ] Nova Receita (não está na navegação principal)

- [ ] Estilizar com tema claro e tipografia simples.

---

### 9. Utilitários
#### 9.1 Constants
- [x] Definir cores, estilos, texto genérico.

#### 9.2 Helpers
- [ ] Funções auxiliares (ex: formatar datas, validar inputs).

---

## 💡 Notas Finais
- Não usar estruturas de navegação complexas.
- Separação rigorosa entre dados, UI e lógica.
- Não incluir testes neste momento.
- Garantir que cada módulo é independente e atómico.

---

## 🔧 Problemas Identificados a Resolver
- [x] **Erro Java**: Android requer Java 17, actualmente usa Java 11 (configurado)
- [x] **Cores personalizadas**: `Colors.myColor1` não definida (causa erros)
- [x] **Assets**: Imagens não declaradas no `pubspec.yaml`
- [x] **Imports**: Limpar imports desnecessários e duplicados
- [x] **Nomenclatura**: Renomear `novaReceita.dart` para `nova_receita.dart` (criado novo)
- [x] **Arquitectura**: Reorganizar ficheiros na estrutura de pastas planeada
- [x] **Dependências**: Adicionar `shared_preferences` ao `pubspec.yaml`
- [x] **Navegação**: Implementar navegação funcional entre ecrãs
- [x] **Funcionalidades**: Implementar CRUD completo para receitas e despensa

## 📊 Estado Actual do Projeto
**Progresso estimado: ~95%**
- ✅ Estrutura básica Flutter
- ✅ Ecrãs de UI funcionais
- ✅ Navegação completa e funcional
- ✅ Modelos de dados (100%)
- ✅ Serviços e lógica de negócio (100%)
- ✅ Armazenamento local (100%)
- ✅ Funcionalidades principais (95%)
- ✅ Constantes e tema da aplicação
- ✅ Lógica de sugestão de receitas
- ✅ Sistema de login/registo funcional
- ✅ CRUD completo para receitas e despensa
- ✅ Formulários com validação
- ✅ Estados de carregamento e erro
- ⚠️ Problemas de IDE (Flutter SDK não detectado)

---