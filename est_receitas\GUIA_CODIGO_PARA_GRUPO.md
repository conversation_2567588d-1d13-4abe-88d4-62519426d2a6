# 💻 **GUIA DO CÓDIGO PARA O GRUPO**

## 🎯 **COMO EXPLICAR O CÓDIGO AOS COLEGAS**

Este guia ajuda-te a explicar o código do projeto EST Receitas aos restantes elementos do grupo de forma simples e clara.

---

## 📁 **ESTRUTURA DE FICHEIROS EXPLICADA**

### **Ficheiros Mais Importantes**
```
lib/
├── main.dart                    # ⭐ INÍCIO - Por onde tudo começa
├── models/                      # 📊 DADOS - Como guardamos informação
│   ├── utilizador.dart         # Informação do utilizador
│   ├── receita.dart            # Estrutura de uma receita
│   └── item_despensa.dart      # Item da despensa/frigorífico
├── services/                    # 🔧 LÓGICA - Como fazemos as coisas
│   ├── hybrid_receita_service.dart    # Gestão híbrida de receitas
│   ├── hybrid_stock_service.dart      # Gestão híbrida de stock
│   └── connectivity_service.dart      # Verificar internet
├── screens/                     # 📱 ECRÃS - O que o utilizador vê
│   ├── home_screen.dart        # Ecrã principal
│   ├── auth/login_screen.dart  # Ecrã de login
│   └── create_recipe.dart      # Criar receita
└── config/
    └── api_config.dart         # 🌐 URLs do servidor
```

---

## 🚀 **COMO EXPLICAR O MAIN.DART**

### **O que é:**
É o **ponto de entrada** da aplicação - o primeiro código que executa.

### **Código Simplificado:**
```dart
void main() async {
  // 1. Preparar Flutter
  WidgetsFlutterBinding.ensureInitialized();
  
  // 2. Preparar armazenamento local
  await StorageService.init();
  
  // 3. Preparar serviços
  ConnectivityService().initialize();
  
  // 4. Executar aplicação
  runApp(const MyApp());
}
```

### **Como Explicar:**
1. **"Primeiro, preparamos o Flutter"** - Como preparar uma cozinha antes de cozinhar
2. **"Depois, preparamos o armazenamento"** - Como abrir o frigorífico para ver o que temos
3. **"Depois, preparamos os serviços"** - Como ligar os eletrodomésticos
4. **"Finalmente, executamos a aplicação"** - Como começar a cozinhar

---

## 📊 **COMO EXPLICAR OS MODELOS**

### **Conceito:**
Os modelos são como **formulários** que definem que informação guardamos.

### **Exemplo: Receita**
```dart
class Receita {
  final String titulo;           // Nome da receita
  final String descricao;        // Descrição breve
  final List<Ingrediente> ingredientes;  // Lista de ingredientes
  final String instrucoes;       // Como fazer
  final int tempoPreparo;        // Tempo em minutos
}
```

### **Como Explicar:**
- **"É como uma ficha de receita"** - tem título, ingredientes, instruções
- **"Cada receita tem uma lista de ingredientes"** - como uma lista de compras
- **"Podemos converter para JSON"** - para enviar ao servidor
- **"Podemos guardar localmente"** - para funcionar offline

### **Exemplo: Item de Despensa**
```dart
class ItemDespensa {
  final String nome;             // Nome do produto
  final double quantidade;       // Quanto temos
  final String unidade;          // kg, litros, unidades
  final DateTime dataValidade;   // Quando expira
  final LocalizacaoItem localizacao;  // Despensa ou Frigorífico
}
```

### **Como Explicar:**
- **"É como uma etiqueta num produto"** - nome, quantidade, validade
- **"Sabe onde está guardado"** - despensa ou frigorífico
- **"Avisa quando está a expirar"** - para evitar desperdício

---

## 🔧 **COMO EXPLICAR OS SERVIÇOS**

### **Conceito:**
Os serviços são como **funcionários especializados** que fazem tarefas específicas.

### **Serviços Híbridos - A Inovação Principal**

#### **Problema:**
E se não há internet? A aplicação deve funcionar sempre!

#### **Solução:**
Cada serviço tem **3 camadas**:

```
┌─────────────────┐
│   HÍBRIDO       │ ← Decide o que fazer
│   (Inteligente) │
├─────────────────┤
│   LOCAL         │ ← Guarda no telemóvel
│   (Sempre)      │
├─────────────────┤
│   API           │ ← Comunica com servidor
│   (Se há net)   │
└─────────────────┘
```

### **Exemplo: HybridReceitaService**
```dart
class HybridReceitaService {
  // Tem 3 "funcionários"
  final ServicoReceitas _localService;     // Funcionário local
  final ApiReceitaService _apiService;     // Funcionário da internet
  final ConnectivityService _connectivity; // Funcionário que verifica internet
  
  // Método principal
  Future<List<Receita>> obterTodasReceitas() async {
    // 1. Sempre pedir ao funcionário local primeiro (rápido)
    final receitasLocais = await _localService.obterReceitas();
    
    // 2. Se há internet, pedir também ao servidor
    if (_connectivity.isOnline) {
      final receitasServidor = await _apiService.obterTodasReceitas();
      // 3. Combinar os dois (servidor tem prioridade)
      return _combinarReceitas(receitasLocais, receitasServidor);
    }
    
    // 4. Se não há internet, usar só as locais
    return receitasLocais;
  }
}
```

### **Como Explicar:**
1. **"Sempre responde rápido"** - usa dados locais primeiro
2. **"Se há internet, melhora a resposta"** - combina com dados do servidor
3. **"Se não há internet, funciona na mesma"** - usa só dados locais
4. **"É inteligente"** - decide automaticamente o que fazer

---

## 📱 **COMO EXPLICAR OS ECRÃS**

### **Conceito:**
Os ecrãs são como **páginas de um livro** - cada um mostra algo diferente.

### **Exemplo: HomeScreen**
```dart
class HomeScreen extends StatefulWidget {
  // Tem estado que pode mudar
}

class _HomeScreenState extends State<HomeScreen> {
  // Variáveis que controlam o que mostramos
  int _currentIndex = 0;        // Que tab está ativo
  bool _isLoading = false;      // Se está a carregar
  int _totalReceitas = 0;       // Quantas receitas temos
  
  // Método que carrega dados
  Future<void> _loadData() async {
    setState(() { _isLoading = true; });  // Mostrar loading
    
    // Pedir dados aos serviços
    final receitas = await _receitaService.obterTodasReceitas();
    
    setState(() {
      _totalReceitas = receitas.length;  // Atualizar contador
      _isLoading = false;                // Esconder loading
    });
  }
  
  // Método que constrói a interface
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('EST Receitas')),
      body: _isLoading 
        ? CircularProgressIndicator()    // Se está a carregar, mostrar spinner
        : _buildContent(),               // Senão, mostrar conteúdo
      bottomNavigationBar: BottomNavigationBar(...),
    );
  }
}
```

### **Como Explicar:**
1. **"StatefulWidget"** - pode mudar (como uma página que se atualiza)
2. **"setState()"** - diz ao Flutter para redesenhar o ecrã
3. **"_loadData()"** - vai buscar informação aos serviços
4. **"build()"** - constrói o que aparece no ecrã
5. **"_isLoading"** - controla se mostra loading ou conteúdo

---

## 🌐 **COMO EXPLICAR A COMUNICAÇÃO COM O SERVIDOR**

### **Conceito:**
É como **enviar uma carta** e **receber uma resposta**.

### **Exemplo: Criar Receita**
```dart
// 1. Flutter prepara a "carta" (JSON)
final receitaJson = receita.toJson();

// 2. Flutter envia a "carta" ao servidor
final response = await http.post(
  Uri.parse('http://localhost:8080/api/receitas'),
  headers: {'Content-Type': 'application/json'},
  body: jsonEncode(receitaJson),
);

// 3. Servidor responde com a receita criada
if (response.statusCode == 201) {
  final receitaCriada = Receita.fromJson(jsonDecode(response.body));
  return receitaCriada;
}
```

### **Como Explicar:**
1. **"Convertemos a receita para JSON"** - como escrever numa linguagem que o servidor entende
2. **"Enviamos via HTTP POST"** - como enviar uma carta registada
3. **"Servidor responde com código 201"** - como receber confirmação de entrega
4. **"Convertemos a resposta de volta"** - como ler a carta de resposta

---

## 🔄 **COMO EXPLICAR A SINCRONIZAÇÃO**

### **Conceito:**
É como **sincronizar dois calendários** - garantir que têm a mesma informação.

### **Estratégia:**
```
OFFLINE:
Utilizador cria receita → Guarda só localmente → Marca para sincronizar

VOLTA ONLINE:
Aplicação deteta internet → Envia receitas pendentes → Atualiza dados locais
```

### **Código Simplificado:**
```dart
Future<Receita> criarReceita(Receita receita) async {
  // 1. SEMPRE guardar localmente primeiro
  final receitaLocal = await _localService.guardarReceita(receita);
  
  // 2. Se há internet, tentar enviar ao servidor
  if (_connectivity.isOnline) {
    try {
      final receitaServidor = await _apiService.criarReceita(receita);
      return receitaServidor;  // Sucesso!
    } catch (e) {
      // 3. Se falhar, marcar para sincronizar depois
      await _marcarParaSincronizacao(receitaLocal);
    }
  }
  
  // 4. Retornar receita local (funciona sempre)
  return receitaLocal;
}
```

### **Como Explicar:**
1. **"Sempre funciona"** - guarda localmente primeiro
2. **"Tenta melhorar"** - envia ao servidor se possível
3. **"Não desiste"** - marca para tentar depois se falhar
4. **"Sincroniza automaticamente"** - quando volta internet

---

## 🎯 **PONTOS-CHAVE PARA EXPLICAR AO GRUPO**

### **1. Arquitetura Híbrida**
- **"É como ter dois cérebros"** - um local (rápido) e um remoto (completo)
- **"Sempre funciona"** - mesmo sem internet
- **"Melhora quando pode"** - sincroniza quando há internet

### **2. Organização do Código**
- **"Cada coisa no seu lugar"** - modelos, serviços, ecrãs separados
- **"Reutilizável"** - widgets que podem ser usados em vários sítios
- **"Fácil de manter"** - cada ficheiro tem uma responsabilidade

### **3. Funcionamento Offline**
- **"Como um frigorífico"** - funciona mesmo quando não há eletricidade
- **"Sincroniza quando pode"** - como carregar o telemóvel quando há tomada
- **"Nunca perde dados"** - tudo fica guardado localmente

### **4. Interface Responsiva**
- **"Mostra o que está a acontecer"** - loading, erros, sucessos
- **"Adapta-se à situação"** - online/offline, com/sem dados
- **"Fácil de usar"** - navegação intuitiva

---

## 🎬 **COMO DEMONSTRAR AO GRUPO**

### **1. Mostrar Estrutura**
```bash
# Abrir VS Code na pasta do projeto
code est_receitas

# Mostrar estrutura de pastas
# Explicar cada pasta brevemente
```

### **2. Mostrar Código Principal**
```bash
# Abrir main.dart
# Explicar fluxo de inicialização

# Abrir home_screen.dart
# Explicar como funciona um ecrã

# Abrir hybrid_receita_service.dart
# Explicar estratégia híbrida
```

### **3. Mostrar Funcionamento**
```bash
# Executar aplicação
flutter run -d chrome

# Demonstrar funcionalidades
# Explicar o que acontece "por trás"
```

---

## 💡 **DICAS PARA EXPLICAR BEM**

### **Use Analogias**
- **Serviços** = Funcionários especializados
- **Modelos** = Formulários/fichas
- **Ecrãs** = Páginas de um livro
- **Sincronização** = Sincronizar calendários
- **Híbrido** = Ter dois cérebros

### **Comece pelo Simples**
1. **"O que faz"** antes de **"como faz"**
2. **Conceito geral** antes de **detalhes técnicos**
3. **Analogias** antes de **código**
4. **Demonstração** antes de **explicação**

### **Use Exemplos Concretos**
- **"Quando criam uma receita..."**
- **"Se não há internet..."**
- **"Quando voltam online..."**
- **"Se o servidor falhar..."**

**🎯 Com este guia, consegues explicar todo o projeto de forma clara e compreensível para qualquer elemento do grupo!** 🇵🇹✨
