@echo off
echo ========================================
echo   SIMPLIFICACAO DO PROJETO EST RECEITAS
echo ========================================
echo.
echo Este script remove ficheiros desnecessarios para simplificar o projeto.
echo.
pause

echo.
echo [1/4] Removendo modelos duplicados (versoes em ingles)...
if exist "lib\models\ingredient.dart" (
    del "lib\models\ingredient.dart"
    echo   - Removido: ingredient.dart
)
if exist "lib\models\recipe.dart" (
    del "lib\models\recipe.dart"
    echo   - Removido: recipe.dart
)
if exist "lib\models\pantry_item.dart" (
    del "lib\models\pantry_item.dart"
    echo   - Removido: pantry_item.dart
)

echo.
echo [2/4] Removendo servicos duplicados e complexos...
if exist "lib\services\recipe_service.dart" (
    del "lib\services\recipe_service.dart"
    echo   - Removido: recipe_service.dart
)
if exist "lib\services\pantry_service.dart" (
    del "lib\services\pantry_service.dart"
    echo   - Removido: pantry_service.dart
)
if exist "lib\services\recipe_suggestion_service.dart" (
    del "lib\services\recipe_suggestion_service.dart"
    echo   - Removido: recipe_suggestion_service.dart
)
if exist "lib\services\api_receita_service.dart" (
    del "lib\services\api_receita_service.dart"
    echo   - Removido: api_receita_service.dart
)
if exist "lib\services\api_stock_service.dart" (
    del "lib\services\api_stock_service.dart"
    echo   - Removido: api_stock_service.dart
)
if exist "lib\services\api_utilizador_service.dart" (
    del "lib\services\api_utilizador_service.dart"
    echo   - Removido: api_utilizador_service.dart
)
if exist "lib\services\base_http_service.dart" (
    del "lib\services\base_http_service.dart"
    echo   - Removido: base_http_service.dart
)

echo.
echo [3/4] Removendo ecras duplicados...
if exist "lib\screens\login_screen.dart" (
    del "lib\screens\login_screen.dart"
    echo   - Removido: login_screen.dart (duplicado)
)
if exist "lib\screens\register_screen.dart" (
    del "lib\screens\register_screen.dart"
    echo   - Removido: register_screen.dart (duplicado)
)
if exist "lib\screens\test_screen.dart" (
    del "lib\screens\test_screen.dart"
    echo   - Removido: test_screen.dart
)

echo.
echo [4/4] Removendo utilitarios e widgets complexos...
if exist "lib\utils\api_test_helper_corrigido.dart" (
    del "lib\utils\api_test_helper_corrigido.dart"
    echo   - Removido: api_test_helper_corrigido.dart
)
if exist "lib\utils\test_validator.dart" (
    del "lib\utils\test_validator.dart"
    echo   - Removido: test_validator.dart
)
if exist "lib\utils\correcoes_temporarias.dart" (
    del "lib\utils\correcoes_temporarias.dart"
    echo   - Removido: correcoes_temporarias.dart
)
if exist "lib\widgets\connectivity_status_widget.dart" (
    del "lib\widgets\connectivity_status_widget.dart"
    echo   - Removido: connectivity_status_widget.dart
)
if exist "lib\widgets\add_ingredient_form.dart" (
    del "lib\widgets\add_ingredient_form.dart"
    echo   - Removido: add_ingredient_form.dart
)

echo.
echo ========================================
echo   SIMPLIFICACAO CONCLUIDA COM SUCESSO!
echo ========================================
echo.
echo Ficheiros removidos:
echo   - 3 modelos duplicados
echo   - 7 servicos complexos
echo   - 3 ecras duplicados/teste
echo   - 5 utilitarios complexos
echo.
echo Total: 18 ficheiros removidos
echo.
echo O projeto agora esta mais simples e focado nas funcionalidades essenciais.
echo.
echo Proximos passos:
echo   1. Executar: flutter pub get
echo   2. Executar: flutter analyze
echo   3. Testar a aplicacao: flutter run -d chrome
echo.
pause
