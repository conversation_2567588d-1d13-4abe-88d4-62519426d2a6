@echo off
REM ========================================================================
REM Script de execução do EST Receitas Backend (Windows)
REM ========================================================================
REM Este script:
REM 1. Verifica se Maven está instalado e disponível no PATH
REM 2. Compila o projeto Spring Boot
REM 3. Inicia o servidor na porta 8080
REM 4. Fornece URLs úteis para teste
REM
REM Pré-requisitos:
REM - Java 17 ou superior
REM - Maven 3.6 ou superior
REM - Variáveis de ambiente JAVA_HOME e MAVEN_HOME configuradas
REM ========================================================================

echo ========================================
echo   EST Receitas Backend - Iniciando...
echo ========================================

REM Verificar se Maven está disponível no PATH do sistema
where mvn >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERRO] Maven não encontrado no PATH
    echo.
    echo Soluções possíveis:
    echo 1. Instalar Maven: https://maven.apache.org/download.cgi
    echo 2. Adicionar Maven ao PATH do sistema
    echo 3. Usar IDE (IntelliJ IDEA, Eclipse, VS Code)
    echo 4. Usar Maven Wrapper: .\mvnw spring-boot:run
    echo.
    pause
    exit /b 1
)

echo [INFO] Maven encontrado!
mvn --version

echo.
echo [INFO] Compilando e iniciando o backend...
echo [INFO] Servidor: http://localhost:8080
echo [INFO] Health Check: http://localhost:8080/api/testes/saude
echo [INFO] Console H2: http://localhost:8080/h2-console
echo [INFO] API Docs: Consultar API_DOCUMENTATION.md
echo.
echo Pressione Ctrl+C para parar o servidor
echo ========================================

REM Executar o backend com Maven
REM O comando spring-boot:run compila e executa a aplicação
mvn spring-boot:run

REM Pausa para manter a janela aberta em caso de erro
pause
