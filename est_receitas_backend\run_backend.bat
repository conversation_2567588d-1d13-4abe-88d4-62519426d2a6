@echo off
echo ========================================
echo   EST Receitas Backend - Iniciando...
echo ========================================

REM Verificar se Maven está disponível
where mvn >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERRO] Maven não encontrado no PATH
    echo.
    echo Soluções possíveis:
    echo 1. Instalar Maven: https://maven.apache.org/download.cgi
    echo 2. Adicionar Maven ao PATH do sistema
    echo 3. Usar IDE (IntelliJ IDEA, Eclipse, VS Code)
    echo.
    pause
    exit /b 1
)

echo [INFO] Maven encontrado!
mvn --version

echo.
echo [INFO] Compilando e iniciando o backend...
echo [INFO] URL: http://localhost:8080
echo [INFO] Health Check: http://localhost:8080/api/testes/saude
echo.
echo Pressione Ctrl+C para parar o servidor
echo ========================================

REM Executar o backend
mvn spring-boot:run

pause
