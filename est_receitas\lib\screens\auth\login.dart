import 'package:flutter/material.dart';
import '../../servicos/hibrido_utilizador_servico.dart';
import '../../widgets/common/loading_widget.dart';
import '../../utils/constants.dart';
import '../home.dart';
import 'registo.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // Chave global para validação do formulário
  final _formKey = GlobalKey<FormState>();

  // Controladores para os campos de texto
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  // Serviço responsável pela autenticação (híbrido: local + API)
  final HibridoUtilizadorServico _utilizadorService =
      HibridoUtilizadorServico();

  // Estado de carregamento durante o processo de login
  bool _isLoading = false;

  // Controla se a password está visível ou oculta
  bool _obscurePassword = true;

  /// Limpa os recursos quando o widget é destruído
  @override
  void dispose() {
    // Liberta a memória dos controladores de texto
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Executa o processo de login
  Future<void> _login() async {
    // Valida o formulário antes de prosseguir
    if (!_formKey.currentState!.validate()) return;

    // Ativa o estado de carregamento
    setState(() {
      _isLoading = true;
    });

    try {
      // Chama o serviço de autenticação
      final resultado = await _utilizadorService.login(
        email: _emailController.text.trim(), // Remove espaços em branco
        password: _passwordController.text,
      );

      // Verifica se o widget ainda está montado antes de atualizar a UI
      if (mounted) {
        if (resultado.sucesso) {
          // Login bem-sucedido: navega para a tela principal
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        } else {
          // Login falhado: mostra mensagem de erro
          _mostrarErro(resultado.mensagem);
        }
      }
    } catch (e) {
      // Trata erros inesperados
      if (mounted) {
        _mostrarErro('Erro inesperado: $e');
      }
    } finally {
      // Desativa o estado de carregamento
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Exibe uma mensagem de erro na parte inferior da tela
  void _mostrarErro(String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red, // Cor vermelha para indicar erro
        duration: const Duration(seconds: 3), // Duração de 3 segundos
      ),
    );
  }

  /// Navega para a tela de registo de novos utilizadores
  void _navegarParaRegisto() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegistoScreen()),
    );
  }

  /// Constrói a interface da tela de login
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50], // Fundo cinza claro
      body: SafeArea(
        child:
            // Mostra loading ou formulário dependendo do estado
            _isLoading
                ? const LoadingWidget(message: 'A fazer login...')
                : SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 60),

                      // === CABEÇALHO COM LOGO E TÍTULO ===
                      Column(
                        children: [
                          // Logo circular com ícone de restaurante
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(60),
                            ),
                            child: const Icon(
                              Icons.restaurant_menu,
                              size: 60,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Título da aplicação
                          const Text(
                            'EST Receitas',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Mensagem de boas-vindas
                          Text(
                            'Bem-vindo!',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 48),

                      // === TÍTULO DA SEÇÃO DE LOGIN ===
                      const Text(
                        'Login',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      // === FORMULÁRIO DE LOGIN ===
                      Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Campo de email
                            TextFormField(
                              controller: _emailController,
                              keyboardType: TextInputType.emailAddress,
                              decoration: InputDecoration(
                                labelText: 'Email',
                                prefixIcon: const Icon(Icons.email),
                                border: OutlineInputBorder(),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Por favor, insira o seu email';
                                }
                                if (!RegExp(
                                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                ).hasMatch(value)) {
                                  return 'Por favor, insira um email válido';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 16),

                            // Campo de password com botão para mostrar/ocultar
                            TextFormField(
                              controller: _passwordController,
                              obscureText: _obscurePassword, // Controla visibilidade da password
                              decoration: InputDecoration(
                                labelText: 'Password',
                                prefixIcon: const Icon(Icons.lock),
                                // Botão para alternar visibilidade da password
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility // Olho fechado
                                        : Icons.visibility_off, // Olho aberto
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                                border: OutlineInputBorder(),
                                filled: true,
                                fillColor: Colors.white,
                              ),
                              // Validação da password
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Por favor, insira a sua password';
                                }
                                if (value.length < 6) {
                                  return 'A password deve ter pelo menos 6 caracteres';
                                }
                                return null;
                              },
                            ),

                            const SizedBox(height: 24),

                            // === BOTÃO DE LOGIN ===
                            SizedBox(
                              width: double.infinity, // Ocupa toda a largura
                              height: 50,
                              child: ElevatedButton(
                                onPressed: _login, // Chama a função de login
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppConstants.primaryColor,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text(
                                  'Entrar',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // === LINK PARA REGISTO ===
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Texto informativo
                          Text(
                            'Não tem conta? ',
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                          // Link clicável para registo
                          GestureDetector(
                            onTap: _navegarParaRegisto, // Navega para tela de registo
                            child: const Text(
                              'Registar-se',
                              style: TextStyle(
                                color: AppConstants.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
      ),
    );
  }
}
