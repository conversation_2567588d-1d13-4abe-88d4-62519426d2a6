import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/utilizador.dart';
import 'servico_armazenamento.dart';

/// Resultado da autenticação
class ResultadoAutenticacao {
  final bool sucesso;
  final String? mensagem;
  final Utilizador? utilizador;
  final String? token;

  const ResultadoAutenticacao({
    required this.sucesso,
    this.mensagem,
    this.utilizador,
    this.token,
  });

  factory ResultadoAutenticacao.sucesso({
    Utilizador? utilizador,
    String? token,
    String? mensagem,
  }) {
    return ResultadoAutenticacao(
      sucesso: true,
      utilizador: utilizador,
      token: token,
      mensagem: mensagem ?? 'Operação realizada com sucesso',
    );
  }

  factory ResultadoAutenticacao.erro(String mensagem) {
    return ResultadoAutenticacao(
      sucesso: false,
      mensagem: mensagem,
    );
  }
}

/// Serviço de autenticação local
class ServicoAutenticacao {
  static final ServicoAutenticacao _instance = ServicoAutenticacao._internal();
  factory ServicoAutenticacao() => _instance;
  ServicoAutenticacao._internal();

  // Usar a instância estática do ServicoArmazenamento

  // Stream para notificar mudanças no estado de autenticação
  final StreamController<Utilizador?> _utilizadorController = StreamController<Utilizador?>.broadcast();
  Stream<Utilizador?> get utilizadorStream => _utilizadorController.stream;

  Utilizador? _utilizadorAtual;
  Utilizador? get utilizadorAtual => _utilizadorAtual;
  bool get isAutenticado => _utilizadorAtual != null;

  // Chaves para armazenamento
  static const String _keyUtilizadorAtual = 'utilizador_atual';
  static const String _keyUtilizadores = 'utilizadores_registados';
  static const String _keyTokens = 'tokens_utilizadores';

  /// Inicializar o serviço
  Future<void> inicializar() async {
    await _carregarUtilizadorAtual();
  }

  /// Registar novo utilizador
  Future<ResultadoAutenticacao> registar({
    required String nome,
    required String email,
    required String password,
    String? telefone,
  }) async {
    try {
      // Verificar se o email já existe
      final utilizadoresExistentes = await _obterUtilizadoresRegistados();
      if (utilizadoresExistentes.any((u) => u.email.toLowerCase() == email.toLowerCase())) {
        return ResultadoAutenticacao.erro('Email já está registado');
      }

      // Criar novo utilizador
      final novoUtilizador = Utilizador(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        nome: nome,
        email: email,preferencias: const PreferenciasUtilizador(),
      );

      // Guardar utilizador
      utilizadoresExistentes.add(novoUtilizador);
      await _guardarUtilizadores(utilizadoresExistentes);

      // Guardar password (hash simples para demonstração)
      await _guardarPassword(novoUtilizador.id!, password);

      // Fazer login automático
      await _definirUtilizadorAtual(novoUtilizador);

      return ResultadoAutenticacao.sucesso(
        utilizador: novoUtilizador,
        mensagem: 'Conta criada com sucesso',
      );
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro ao criar conta: $e');
    }
  }

  /// Fazer login
  Future<ResultadoAutenticacao> login({
    required String email,
    required String password,
  }) async {
    try {
      // Procurar utilizador
      final utilizadores = await _obterUtilizadoresRegistados();
      final utilizador = utilizadores.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('Utilizador não encontrado'),
      );

      // Verificar password
      final passwordGuardada = await _obterPassword(utilizador.id!);
      if (passwordGuardada != password) {
        return ResultadoAutenticacao.erro('Password incorreta');
      }

      // Atualizar último acesso
      final utilizadorAtualizado = utilizador.copyWith(
        dataUltimoAcesso: DateTime.now(),
      );

      // Atualizar na lista
      final index = utilizadores.indexWhere((u) => u.id == utilizador.id);
      utilizadores[index] = utilizadorAtualizado;
      await _guardarUtilizadores(utilizadores);

      // Definir como utilizador atual
      await _definirUtilizadorAtual(utilizadorAtualizado);

      return ResultadoAutenticacao.sucesso(
        utilizador: utilizadorAtualizado,
        mensagem: 'Login realizado com sucesso',
      );
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro no login: $e');
    }
  }

  /// Fazer logout
  Future<void> logout() async {
    _utilizadorAtual = null;
    await ServicoArmazenamento.delete(_keyUtilizadorAtual);
    _utilizadorController.add(null);
  }

  /// Atualizar perfil do utilizador
  Future<ResultadoAutenticacao> atualizarPerfil(Utilizador utilizadorAtualizado) async {
    try {
      if (_utilizadorAtual == null) {
        return ResultadoAutenticacao.erro('Utilizador não autenticado');
      }

      // Atualizar na lista de utilizadores
      final utilizadores = await _obterUtilizadoresRegistados();
      final index = utilizadores.indexWhere((u) => u.id == utilizadorAtualizado.id);

      if (index == -1) {
        return ResultadoAutenticacao.erro('Utilizador não encontrado');
      }

      utilizadores[index] = utilizadorAtualizado;
      await _guardarUtilizadores(utilizadores);

      // Atualizar utilizador atual
      await _definirUtilizadorAtual(utilizadorAtualizado);

      return ResultadoAutenticacao.sucesso(
        utilizador: utilizadorAtualizado,
        mensagem: 'Perfil atualizado com sucesso',
      );
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro ao atualizar perfil: $e');
    }
  }

  /// Alterar password
  Future<ResultadoAutenticacao> alterarPassword({
    required String passwordAtual,
    required String novaPassword,
  }) async {
    try {
      if (_utilizadorAtual == null) {
        return ResultadoAutenticacao.erro('Utilizador não autenticado');
      }

      // Verificar password atual
      final passwordGuardada = await _obterPassword(_utilizadorAtual!.id!);
      if (passwordGuardada != passwordAtual) {
        return ResultadoAutenticacao.erro('Password atual incorreta');
      }

      // Guardar nova password
      await _guardarPassword(_utilizadorAtual!.id!, novaPassword);

      return ResultadoAutenticacao.sucesso(
        utilizador: _utilizadorAtual!,
        mensagem: 'Password alterada com sucesso',
      );
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro ao alterar password: $e');
    }
  }

  /// Eliminar conta
  Future<ResultadoAutenticacao> eliminarConta(String password) async {
    try {
      if (_utilizadorAtual == null) {
        return ResultadoAutenticacao.erro('Utilizador não autenticado');
      }

      // Verificar password
      final passwordGuardada = await _obterPassword(_utilizadorAtual!.id!);
      if (passwordGuardada != password) {
        return ResultadoAutenticacao.erro('Password incorreta');
      }

      // Remover utilizador da lista
      final utilizadores = await _obterUtilizadoresRegistados();
      utilizadores.removeWhere((u) => u.id == _utilizadorAtual!.id);
      await _guardarUtilizadores(utilizadores);

      // Remover password
      await ServicoArmazenamento.delete('password_${_utilizadorAtual!.id}');

      // Fazer logout
      await logout();

      return ResultadoAutenticacao.sucesso(
        utilizador: null,
        mensagem: 'Conta eliminada com sucesso',
      );
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro ao eliminar conta: $e');
    }
  }

  /// Verificar se é o primeiro utilizador (será administrador)
  Future<bool> isPrimeiroUtilizador() async {
    final utilizadores = await _obterUtilizadoresRegistados();
    return utilizadores.isEmpty;
  }

  // ===== MÉTODOS PRIVADOS =====

  Future<void> _carregarUtilizadorAtual() async {
    try {
      final utilizador = await ServicoArmazenamento.loadObject<Utilizador>(
        _keyUtilizadorAtual,
        (map) => Utilizador.fromMap(map),
      );
      if (utilizador != null) {
        _utilizadorAtual = utilizador;
        _utilizadorController.add(_utilizadorAtual);
      }
    } catch (e) {
      // Log do erro (em produção usar um sistema de logging adequado)
      debugPrint('Erro ao carregar utilizador atual: $e');
    }
  }

  Future<void> _definirUtilizadorAtual(Utilizador utilizador) async {
    _utilizadorAtual = utilizador;
    await ServicoArmazenamento.saveObject<Utilizador>(
      _keyUtilizadorAtual,
      utilizador,
      (u) => u.toMap(),
    );
    _utilizadorController.add(utilizador);
  }

  Future<List<Utilizador>> _obterUtilizadoresRegistados() async {
    try {
      return await ServicoArmazenamento.loadObjectList<Utilizador>(
        _keyUtilizadores,
        (map) => Utilizador.fromMap(map),
      );
    } catch (e) {
      // Log do erro (em produção usar um sistema de logging adequado)
      debugPrint('Erro ao obter utilizadores: $e');
      return [];
    }
  }

  Future<void> _guardarUtilizadores(List<Utilizador> utilizadores) async {
    await ServicoArmazenamento.saveObjectList<Utilizador>(
      _keyUtilizadores,
      utilizadores,
      (u) => u.toMap(),
    );
  }

  Future<void> _guardarPassword(String utilizadorId, String password) async {
    // Em produção, usar hash seguro (bcrypt, etc.)
    await ServicoArmazenamento.setString('password_$utilizadorId', password);
  }

  Future<String?> _obterPassword(String utilizadorId) async {
    return await ServicoArmazenamento.getString('password_$utilizadorId');
  }

  /// Limpar todos os dados (para desenvolvimento/testes)
  Future<void> limparTodosDados() async {
    await ServicoArmazenamento.delete(_keyUtilizadorAtual);
    await ServicoArmazenamento.delete(_keyUtilizadores);
    await ServicoArmazenamento.delete(_keyTokens);
    _utilizadorAtual = null;
    _utilizadorController.add(null);
  }

  void dispose() {
    _utilizadorController.close();
  }
}

// Aliases para compatibilidade
typedef AuthServico = ServicoAutenticacao;
typedef AuthResult = ResultadoAutenticacao;
