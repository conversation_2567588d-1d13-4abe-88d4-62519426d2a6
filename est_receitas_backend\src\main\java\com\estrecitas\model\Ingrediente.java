package com.estrecitas.model;

import jakarta.persistence.*;

@Entity
@Table(name = "ingredientes")
public class Ingrediente {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String nome;
    
    @Column(nullable = false)
    private Double quantidade;
    
    @Column(nullable = false, length = 50)
    private String unidade;
    
    @Column(length = 500)
    private String observacoes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "receita_id", nullable = false)
    private Receita receita;
    
    // Construtores
    public Ingrediente() {}
    
    public Ingrediente(String nome, Double quantidade, String unidade) {
        this.nome = nome;
        this.quantidade = quantidade;
        this.unidade = unidade;
    }
    
    public Ingrediente(String nome, Double quantidade, String unidade, Receita receita) {
        this(nome, quantidade, unidade);
        this.receita = receita;
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Double getQuantidade() {
        return quantidade;
    }
    
    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
    
    public String getUnidade() {
        return unidade;
    }
    
    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }
    
    public String getObservacoes() {
        return observacoes;
    }
    
    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }
    
    public Receita getReceita() {
        return receita;
    }
    
    public void setReceita(Receita receita) {
        this.receita = receita;
    }
    
    // Métodos auxiliares
    public String getQuantidadeFormatada() {
        if (quantidade == null) return "";
        
        // Se for um número inteiro, não mostrar casas decimais
        if (quantidade % 1 == 0) {
            return String.valueOf(quantidade.intValue());
        }
        
        return String.valueOf(quantidade);
    }
    
    public String getDescricaoCompleta() {
        StringBuilder sb = new StringBuilder();
        sb.append(getQuantidadeFormatada());
        
        if (unidade != null && !unidade.trim().isEmpty()) {
            sb.append(" ").append(unidade);
        }
        
        if (nome != null && !nome.trim().isEmpty()) {
            sb.append(" de ").append(nome);
        }
        
        if (observacoes != null && !observacoes.trim().isEmpty()) {
            sb.append(" (").append(observacoes).append(")");
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "Ingrediente{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", quantidade=" + quantidade +
                ", unidade='" + unidade + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Ingrediente that = (Ingrediente) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (nome != null ? !nome.equals(that.nome) : that.nome != null) return false;
        if (quantidade != null ? !quantidade.equals(that.quantidade) : that.quantidade != null) return false;
        return unidade != null ? unidade.equals(that.unidade) : that.unidade == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (nome != null ? nome.hashCode() : 0);
        result = 31 * result + (quantidade != null ? quantidade.hashCode() : 0);
        result = 31 * result + (unidade != null ? unidade.hashCode() : 0);
        return result;
    }
}
