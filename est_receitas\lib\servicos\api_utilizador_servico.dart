import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/utilizador.dart';
import '../config/api_config.dart';

/// Serviço de API para comunicação com o backend Spring Boot - Utilizadores
class ApiUtilizadorServico {
  
  // URLs específicas para autenticação (usando novos endpoints em português)
  static String get _registoUrl => ApiConfig.registoUrl;
  static String get _loginUrl => ApiConfig.loginUrl;
  static String get _verificarUrl => ApiConfig.verificarTokenUrl;
  static String get _logoutUrl => ApiConfig.logoutUrl;
  static String get _perfilUrl => ApiConfig.perfilUrl;
  static String get _verificarEmailUrl => ApiConfig.verificarEmailUrl;
  static String get _estatisticasUrl => ApiConfig.estatisticasUrl;
  
  // ===== OPERAÇÕES DE AUTENTICAÇÃO =====
  
  /// Registar novo utilizador
  Future<AuthResponse> registarUtilizador({
    required String nome,
    required String email,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final body = json.encode({
        'nome': nome,
        'email': email,
        'password': password,
        'confirmPassword': confirmPassword,
      });
      
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST $_registoUrl');
        if (ApiConfig.logRequestBody) {
          debugPrint('📤 Body: ${body.replaceAll('"password":"$password"', '"password":"[PROTECTED]"')}');
        }
      }

      final response = await http.post(
        Uri.parse(_registoUrl),
        headers: ApiConfig.jsonHeaders,
        body: body,
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode == 201) {
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Utilizador registado com sucesso');
        }
        return AuthResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Erro no registo');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro no registo: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Login de utilizador
  Future<AuthResponse> login({
    required String email,
    required String password,
  }) async {
    try {
      final body = json.encode({
        'email': email,
        'password': password,
      });
      
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST $_loginUrl');
        if (ApiConfig.logRequestBody) {
          debugPrint('📤 Body: ${body.replaceAll('"password":"$password"', '"password":"[PROTECTED]"')}');
        }
      }

      final response = await http.post(
        Uri.parse(_loginUrl),
        headers: ApiConfig.jsonHeaders,
        body: body,
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Login realizado com sucesso');
        }
        return AuthResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Credenciais inválidas');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro no login: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Verificar token
  Future<AuthResponse> verificarToken(String token) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST $_verificarUrl');
      }

      final response = await http.post(
        Uri.parse(_verificarUrl),
        headers: {
          ...ApiConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      final Map<String, dynamic> data = json.decode(response.body);
      
      if (response.statusCode == 200) {
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Token válido');
        }
        return AuthResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Token inválido');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro na verificação do token: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Logout
  Future<bool> logout(String token) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST $_logoutUrl');
      }

      final response = await http.post(
        Uri.parse(_logoutUrl),
        headers: {
          ...ApiConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Logout realizado com sucesso');
        }
        return true;
      } else {
        return false;
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro no logout: $e');
      }
      return false;
    }
  }

  // ===== OPERAÇÕES DE PERFIL =====

  /// Obter perfil do utilizador
  Future<Utilizador> obterPerfil(String token) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET $_perfilUrl');
      }

      final response = await http.get(
        Uri.parse(_perfilUrl),
        headers: {
          ...ApiConfig.defaultHeaders,
          'Authorization': 'Bearer $token',
        },
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Perfil obtido com sucesso');
        }
        return _utilizadorFromBackendJson(data);
      } else {
        throw Exception('Erro ao obter perfil: ${response.statusCode}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao obter perfil: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Atualizar perfil do utilizador
  Future<Utilizador> atualizarPerfil({
    required String token,
    required String nome,
    required String email,
  }) async {
    try {
      final body = json.encode({
        'nome': nome,
        'email': email,
      });
      
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 PUT $_perfilUrl');
        if (ApiConfig.logRequestBody) {
          debugPrint('📤 Body: $body');
        }
      }

      final response = await http.put(
        Uri.parse(_perfilUrl),
        headers: {
          ...ApiConfig.jsonHeaders,
          'Authorization': 'Bearer $token',
        },
        body: body,
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Perfil atualizado com sucesso');
        }
        return _utilizadorFromBackendJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erro ao atualizar perfil');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao atualizar perfil: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== OPERAÇÕES AUXILIARES =====

  /// Verificar se email existe
  Future<bool> verificarSeEmailExiste(String email) async {
    try {
      final url = ApiConfig.buildUrl(_verificarEmailUrl, {'email': email});

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        return json.decode(response.body) as bool;
      } else {
        return false;
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao verificar email: $e');
      }
      return false;
    }
  }

  /// Verificar conectividade com a API
  Future<bool> verificarConectividade() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.verificarSaudeUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.connectTimeout);

      return response.statusCode == 200;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro de conectividade: $e');
      }
      return false;
    }
  }

  // ===== MÉTODOS PRIVADOS DE CONVERSÃO =====

  /// Converte utilizador do backend para modelo Flutter
  Utilizador _utilizadorFromBackendJson(Map<String, dynamic> json) {
    return Utilizador(
      id: json['id']?.toString(),
      nome: json['nome'] ?? '',
      email: json['email'] ?? '',
    );
  }
}

/// Classe para resposta de autenticação
class AuthResponse {
  final String? token;
  final String? type;
  final String? userId;
  final String? nome;
  final String? email;
  final bool success;
  final String message;

  AuthResponse({
    this.token,
    this.type,
    this.userId,
    this.nome,
    this.email,
    required this.success,
    required this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      token: json['token'],
      type: json['type'],
      userId: json['userId']?.toString(),
      nome: json['nome'],
      email: json['email'],
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  bool get isSuccess => success && token != null;
}
