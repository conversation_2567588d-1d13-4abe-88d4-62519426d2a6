package com.estrecitas.repository;

import com.estrecitas.model.Receita;
import com.estrecitas.model.DificuldadeReceita;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReceitaRepository extends JpaRepository<Receita, Long> {
    
    // Procurar receitas por título
    List<Receita> findByTituloContainingIgnoreCase(String titulo);
    
    // Procurar receitas por categoria
    List<Receita> findByCategoriaIgnoreCase(String categoria);
    

    
    // Procurar receitas por tempo de preparo máximo
    List<Receita> findByTempoPreparoLessThanEqual(Integer tempoMaximo);
    
    // Procurar receitas por número de porções
    List<Receita> findByNumeroPorcoes(Integer numeroPorcoes);
    

    

    
    // Procurar receitas com ingredientes específicos
    @Query("SELECT r, COUNT(i) as matchCount FROM Receita r JOIN r.ingredientes i " +
           "WHERE LOWER(i.nome) IN :ingredientesDisponiveis " +
           "GROUP BY r.id " +
           "ORDER BY matchCount DESC")
    List<Object[]> findReceitasComIngredientesDisponiveis(@Param("ingredientesDisponiveis") List<String> ingredientesDisponiveis);
    
    // Procurar receitas por múltiplos critérios
    @Query("SELECT r FROM Receita r WHERE " +
           "(:titulo IS NULL OR LOWER(r.titulo) LIKE LOWER(CONCAT('%', :titulo, '%'))) AND " +
           "(:categoria IS NULL OR LOWER(r.categoria) = LOWER(:categoria)) AND " +
           "(:dificuldade IS NULL OR r.dificuldade = :dificuldade) AND " +
           "(:tempoMaximo IS NULL OR r.tempoPreparo <= :tempoMaximo)")
    List<Receita> findByMultiplosCriterios(
        @Param("titulo") String titulo,
        @Param("categoria") String categoria,
        @Param("dificuldade") DificuldadeReceita dificuldade,
        @Param("tempoMaximo") Integer tempoMaximo
    );
    
    // Procurar todas as categorias distintas
    @Query("SELECT DISTINCT r.categoria FROM Receita r WHERE r.categoria IS NOT NULL ORDER BY r.categoria")
    List<String> findDistinctCategorias();
    
    // Procurar receitas mais recentes
    @Query("SELECT r FROM Receita r ORDER BY r.id DESC")
    List<Receita> findReceitasRecentes();
    
    // Contar receitas por categoria
    @Query("SELECT r.categoria, COUNT(r) FROM Receita r WHERE r.categoria IS NOT NULL GROUP BY r.categoria")
    List<Object[]> countReceitasPorCategoria();
}
