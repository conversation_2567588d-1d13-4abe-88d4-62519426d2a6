# 🎯 Perguntas Técnicas - EST Receitas

## 📱 Frontend (Flutter/Dart)

### **Arquitetura e Padrões**
1. **Qual padrão arquitetural foi implementado na aplicação?**
   - R: Arquitetura híbrida em camadas (UI → Service → Data) com serviços offline-first

2. **Como funciona a estratégia híbrida de dados?**
   - R: Dados salvos localmente primeiro, sincronização com backend quando possível, fallback gracioso

3. **Que padrão de gestão de estado foi utilizado?**
   - R: StreamController/StreamBuilder para estado reativo, sem bibliotecas externas como BLoC ou Provider

4. **Como é implementada a navegação na aplicação?**
   - R: Navigator com rotas nomeadas ('/login', '/home') e navegação stack-based

5. **Qual a diferença entre os serviços API e os serviços híbridos?**
   - R: API services fazem apenas comunicação HTTP, híbridos combinam local + remoto com fallback

### **Modelos de Dados**
6. **Como funciona a serialização de dados nos modelos?**
   - R: Métodos toJson()/fromJson() para API, toMap()/fromMap() para armazenamento local

7. **Que estratégia é usada para imutabilidade dos modelos?**
   - R: Método copyWith() que cria nova instância com campos alterados

8. **Como são tratados os relacionamentos entre entidades?**
   - R: Receita contém List<Ingrediente>, relacionamentos one-to-many implementados

9. **Que validações são implementadas nos modelos?**
   - R: Validação de email, campos obrigatórios, tipos de dados, enums para dificuldade

### **Armazenamento Local**
10. **Que tecnologia é usada para persistência local?**
    - R: SharedPreferences com abstração através do ServicoArmazenamento

11. **Como funciona o ServicoArmazenamento?**
    - R: Wrapper do SharedPreferences com serialização automática JSON ↔ Map

12. **Que dados são armazenados localmente?**
    - R: Utilizadores, utilizador atual, receitas (cache), configurações da aplicação

13. **Como é garantida a consistência dos dados locais?**
    - R: Operações atómicas, validação antes de salvar, sincronização periódica

### **Comunicação HTTP**
14. **Que biblioteca é usada para requests HTTP?**
    - R: Biblioteca nativa 'http' do Dart

15. **Como são tratados os timeouts e erros de rede?**
    - R: Timeouts configurados (10s conexão, 30s resposta), try-catch com fallback local

16. **Que headers são enviados nas requests?**
    - R: Content-Type: application/json, Accept: application/json, Authorization quando disponível

17. **Como funciona a autenticação com tokens?**
    - R: JWT token salvo após login, enviado no header Authorization para requests autenticadas

### **Interface do Utilizador**
18. **Que design system foi implementado?**
    - R: Material Design 3 com tema customizado baseado em cor primária

19. **Como é implementada a responsividade?**
    - R: Básica com widgets flexíveis, sem breakpoints específicos para tablets

20. **Que widgets reutilizáveis foram criados?**
    - R: ReceitaCard, LoadingWidget, componentes comuns para consistência

21. **Como funciona a validação de formulários?**
    - R: TextFormField com validators, GlobalKey<FormState> para validação

### **Gestão de Estado e Ciclo de Vida**
22. **Como é gerido o estado do utilizador autenticado?**
    - R: HibridoUtilizadorServico com StreamController, widgets escutam via StreamBuilder

23. **Como funciona o ciclo de vida da aplicação?**
    - R: SplashScreen → verificação autenticação → navegação automática

24. **Que estratégia é usada para loading states?**
    - R: LoadingWidget centralizado, estados de carregamento em cada tela

25. **Como são tratadas as mudanças de conectividade?**
    - R: Básico, sem verificação ativa de conectividade (área de melhoria)

---

## 🖥️ Backend (Spring Boot/Java)

### **Arquitetura e Estrutura**
26. **Que arquitetura foi implementada no backend?**
    - R: REST API com Spring Boot, arquitetura em camadas (Controller → Service → Repository)

27. **Como está organizada a estrutura de packages?**
    - R: Por funcionalidade (auth, receitas, stock) com separação de responsabilidades

28. **Que padrão de design foi usado para os controllers?**
    - R: REST controllers com anotações Spring (@RestController, @RequestMapping)

29. **Como funciona a injeção de dependências?**
    - R: Spring IoC container com @Autowired e @Service/@Repository

### **Base de Dados**
30. **Que base de dados é utilizada?**
    - R: Provavelmente PostgreSQL ou MySQL (configurável via application.properties)

31. **Como são mapeadas as entidades JPA?**
    - R: @Entity, @Table, relacionamentos @OneToMany/@ManyToOne

32. **Que estratégia de geração de IDs é usada?**
    - R: @GeneratedValue com IDENTITY ou SEQUENCE

33. **Como são tratadas as transações?**
    - R: @Transactional nos métodos de serviço

### **Autenticação e Segurança**
34. **Como funciona a autenticação JWT?**
    - R: Login gera token JWT, validação em requests subsequentes

35. **Que algoritmo é usado para hash de passwords?**
    - R: BCrypt (padrão Spring Security)

36. **Como é implementada a autorização?**
    - R: Spring Security com roles/authorities, @PreAuthorize

37. **Que medidas de segurança estão implementadas?**
    - R: CORS configurado, validação de inputs, sanitização de dados

### **API REST**
38. **Que endpoints estão disponíveis?**
    - R: /api/autenticacao (login, registo), /api/receitas (CRUD), /api/stock (gestão)

39. **Como são estruturadas as responses da API?**
    - R: DTOs padronizados, ResponseEntity com status codes apropriados

40. **Que códigos de status HTTP são retornados?**
    - R: 200 (OK), 201 (Created), 400 (Bad Request), 401 (Unauthorized), 404 (Not Found), 500 (Error)

41. **Como é implementada a paginação?**
    - R: Pageable do Spring Data, parâmetros page/size/sort

### **Validação e Tratamento de Erros**
42. **Como são validados os dados de entrada?**
    - R: Bean Validation (@Valid, @NotNull, @Email, etc.)

43. **Como são tratadas as exceções?**
    - R: @ControllerAdvice com @ExceptionHandler para tratamento global

44. **Que logs são gerados pela aplicação?**
    - R: SLF4J/Logback com níveis INFO, DEBUG, ERROR

### **Performance e Otimização**
45. **Como é otimizada a performance das queries?**
    - R: JPA queries otimizadas, índices na base de dados, lazy loading

46. **Que estratégias de cache são implementadas?**
    - R: Cache de segundo nível do Hibernate, @Cacheable do Spring

47. **Como é monitorizada a aplicação?**
    - R: Spring Actuator para métricas e health checks

---

## 🔄 Integração Frontend-Backend

### **Comunicação e Protocolos**
48. **Como é estabelecida a comunicação entre frontend e backend?**
    - R: HTTP REST API com JSON, requests assíncronas

49. **Que estratégia é usada para sincronização de dados?**
    - R: Eventual consistency, last-write-wins para conflitos

50. **Como são tratados os conflitos de dados?**
    - R: Atualmente last-write-wins, pode ser melhorado com timestamps

51. **Que mecanismo de retry é implementado?**
    - R: Básico no frontend, pode ser melhorado com exponential backoff

### **Segurança End-to-End**
52. **Como é garantida a segurança na comunicação?**
    - R: HTTPS em produção, validação de tokens JWT

53. **Que dados são criptografados?**
    - R: Passwords com BCrypt no backend, tokens JWT assinados

54. **Como é implementada a gestão de sessões?**
    - R: Stateless com JWT tokens, sem sessões no servidor

### **Tratamento de Erros e Resilência**
55. **Como são tratados os erros de conectividade?**
    - R: Try-catch no frontend, fallback para dados locais

56. **Que estratégias de resilência estão implementadas?**
    - R: Circuit breaker básico, timeout configurável, retry manual

57. **Como é garantida a disponibilidade offline?**
    - R: Cache local completo, sincronização quando conectividade retorna

---

## 🧪 Testes e Qualidade

### **Testes Frontend**
58. **Que tipos de testes estão implementados no Flutter?**
    - R: Atualmente nenhum (área de melhoria), widget_test.dart básico

59. **Como seria implementado o teste de widgets?**
    - R: testWidgets() com WidgetTester, mockito para serviços

60. **Que estratégia de teste seria usada para serviços?**
    - R: Unit tests com mocks, integration tests para fluxos completos

### **Testes Backend**
61. **Que framework de testes é usado no Spring Boot?**
    - R: JUnit 5, Mockito, Spring Boot Test

62. **Como são testados os controllers?**
    - R: @WebMvcTest com MockMvc

63. **Como são testados os serviços?**
    - R: @ExtendWith(MockitoExtension.class) com mocks

64. **Como são testadas as integrações com base de dados?**
    - R: @DataJpaTest com base de dados em memória (H2)

### **Qualidade de Código**
65. **Que ferramentas de análise estática são usadas?**
    - R: Flutter: flutter_lints, Backend: SonarQube/SpotBugs

66. **Como é garantida a cobertura de testes?**
    - R: Jacoco para backend, flutter test --coverage para frontend

67. **Que métricas de qualidade são monitorizadas?**
    - R: Cobertura de código, complexidade ciclomática, code smells

---

## 🚀 DevOps e Deployment

### **Build e CI/CD**
68. **Como é feito o build da aplicação Flutter?**
    - R: flutter build apk/web/ios, configuração por plataforma

69. **Que pipeline de CI/CD seria implementado?**
    - R: GitHub Actions/GitLab CI com stages: test → build → deploy

70. **Como é feito o deployment do backend?**
    - R: JAR executável, Docker container, deploy em cloud (AWS/Azure)

### **Monitorização e Logs**
71. **Como são monitorizadas as aplicações em produção?**
    - R: APM tools (New Relic, DataDog), logs centralizados

72. **Que métricas são importantes monitorizar?**
    - R: Response time, error rate, throughput, resource usage

73. **Como é implementado o logging distribuído?**
    - R: Correlation IDs, structured logging, ELK stack

---

## 🔮 Melhorias e Evolução

### **Próximas Funcionalidades**
74. **Como seria implementado o sistema de notificações?**
    - R: Firebase Cloud Messaging, push notifications

75. **Como seria implementada a partilha de receitas?**
    - R: Endpoints de partilha, sistema de permissões, social features

76. **Como seria implementado o modo offline completo?**
    - R: Service workers (web), background sync, conflict resolution

### **Escalabilidade**
77. **Como escalar a aplicação para mais utilizadores?**
    - R: Load balancing, database sharding, microservices

78. **Que otimizações de performance seriam implementadas?**
    - R: CDN para imagens, cache distribuído (Redis), query optimization

79. **Como seria implementada a multi-tenancy?**
    - R: Tenant isolation, schema per tenant, shared database

### **Segurança Avançada**
80. **Como melhorar a segurança da aplicação?**
    - R: 2FA, rate limiting, security headers, penetration testing

---

*Estas perguntas cobrem os aspectos técnicos fundamentais do projeto EST Receitas, desde implementação básica até conceitos avançados de arquitetura e escalabilidade.*
