package com.estrecitas;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Classe principal da aplicação Spring Boot - EST Receitas Backend
 *
 * Esta é a classe de entrada da aplicação que configura e inicia o servidor Spring Boot.
 * A anotação @SpringBootApplication combina três anotações importantes:
 * - @Configuration: Marca a classe como fonte de definições de beans
 * - @EnableAutoConfiguration: Ativa a configuração automática do Spring Boot
 * - @ComponentScan: Ativa o scan de componentes no package atual e subpackages
 *
 * A aplicação fornece APIs REST para:
 * - Gestão de utilizadores (registo, login, perfil)
 * - Gestão de receitas (CRUD completo)
 * - Gestão de stock/despensa (CRUD completo)
 * - Autenticação JWT
 * - Integração com base de dados H2 (desenvolvimento) ou MySQL (produção)
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@SpringBootApplication
public class EstReceitasBackendApplication {

    /**
     * Método principal que inicia a aplicação Spring Boot
     *
     * @param args argumentos da linha de comando (não utilizados)
     */
    public static void main(String[] args) {
        SpringApplication.run(EstReceitasBackendApplication.class, args);
    }
}
