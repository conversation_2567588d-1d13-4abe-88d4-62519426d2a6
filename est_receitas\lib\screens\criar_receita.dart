import 'package:flutter/material.dart';
import '../models/receita.dart';
import '../models/ingrediente.dart';
import '../utils/constants.dart';
import '../servicos/hibrido_receita_servico.dart';

class CriarReceitaScreen extends StatefulWidget {
  const CriarReceitaScreen({super.key});

  @override
  State<CriarReceitaScreen> createState() => _CriarReceitaScreenState();
}

class _CriarReceitaScreenState extends State<CriarReceitaScreen> {
  final _formKey = GlobalKey<FormState>();
  final _tituloController = TextEditingController();
  final _descricaoController = TextEditingController();
  final _preparacaoController = TextEditingController();

  final List<Ingrediente> _ingredientes = [];
  final _nomeIngredienteController = TextEditingController();
  final _quantidadeController = TextEditingController();
  final _unidadeController = TextEditingController();

  final _numeroPorcoesController = TextEditingController();
  final _tempoPreparoController = TextEditingController();
  String? _dificuldadeSelecionada;
  final _imagemUrlController = TextEditingController();

  @override
  void dispose() {
    _tituloController.dispose();
    _descricaoController.dispose();
    _preparacaoController.dispose();
    _nomeIngredienteController.dispose();
    _quantidadeController.dispose();
    _unidadeController.dispose();
    _numeroPorcoesController.dispose();
    _tempoPreparoController.dispose();
    _imagemUrlController.dispose();
    super.dispose();
  }

  void _adicionarIngrediente() {
    final nome = _nomeIngredienteController.text.trim();
    final quantidadeText = _quantidadeController.text.trim();
    final unidade = _unidadeController.text.trim();

    if (nome.isNotEmpty && quantidadeText.isNotEmpty && unidade.isNotEmpty) {
      final quantidade = double.tryParse(quantidadeText);
      if (quantidade != null && quantidade > 0) {
        setState(() {
          _ingredientes.add(
            Ingrediente(nome: nome, quantidade: quantidade, unidade: unidade),
          );
          _nomeIngredienteController.clear();
          _quantidadeController.clear();
          _unidadeController.clear();
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Quantidade deve ser um número válido')),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Preencha todos os campos do ingrediente'),
        ),
      );
    }
  }

  void _removerIngrediente(int index) {
    setState(() {
      _ingredientes.removeAt(index);
    });
  }

  Future<void> _guardarReceita() async {
    if (_formKey.currentState!.validate()) {
      if (_ingredientes.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Adicione pelo menos um ingrediente')),
        );
        return;
      }
      final receita = Receita(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        titulo: _tituloController.text.trim(),
        descricao: _descricaoController.text.trim(),
        ingredientes: List.from(_ingredientes),
        instrucoes: _preparacaoController.text.trim(),
        numeroPorcoes: int.tryParse(_numeroPorcoesController.text.trim()),
        tempoPreparo: int.tryParse(_tempoPreparoController.text.trim()),
        dificuldade: _dificuldadeSelecionada,
        imagemUrl: _imagemUrlController.text.trim().isNotEmpty ? _imagemUrlController.text.trim() : null,
      );

      try {
        // Guardar a receita usando o serviço
        final receitaService = HibridoReceitaService();
        await receitaService.criarReceita(receita);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Receita guardada com sucesso!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erro ao guardar receita. Tente novamente.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erro inesperado. Tente novamente.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Nova Receita',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Título
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Informações Básicas',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _tituloController,
                        decoration: const InputDecoration(
                          labelText: 'Título da Receita',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.restaurant_menu),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Insira o título da receita';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _descricaoController,
                        decoration: const InputDecoration(
                          labelText: 'Descrição',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.description),
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Insira uma descrição';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Ingredientes
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Ingredientes',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Formulário para adicionar ingrediente
                      Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: TextFormField(
                              controller: _nomeIngredienteController,
                              decoration: const InputDecoration(
                                labelText: 'Nome',
                                border: OutlineInputBorder(),
                                isDense: true,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            flex: 2,
                            child: TextFormField(
                              controller: _quantidadeController,
                              decoration: const InputDecoration(
                                labelText: 'Qtd',
                                border: OutlineInputBorder(),
                                isDense: true,
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            flex: 2,
                            child: TextFormField(
                              controller: _unidadeController,
                              decoration: const InputDecoration(
                                labelText: 'Unidade',
                                border: OutlineInputBorder(),
                                isDense: true,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: _adicionarIngrediente,
                            icon: const Icon(
                              Icons.add_circle,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Lista de ingredientes
                      if (_ingredientes.isNotEmpty) ...[
                        const Text(
                          'Ingredientes Adicionados:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _ingredientes.length,
                          itemBuilder: (context, index) {
                            final ingrediente = _ingredientes[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                title: Text(ingrediente.nome),
                                subtitle: Text(
                                  '${ingrediente.quantidade} ${ingrediente.unidade}',
                                ),
                                trailing: IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed: () => _removerIngrediente(index),
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Preparação
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Modo de Preparação',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _preparacaoController,
                        decoration: const InputDecoration(
                          labelText: 'Instruções de preparação',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.list_alt),
                          alignLabelWithHint: true,
                        ),
                        maxLines: 6,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Insira as instruções de preparação';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Detalhes adicionais
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Detalhes Adicionais',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _numeroPorcoesController,
                              decoration: const InputDecoration(
                                labelText: 'Nº Porções',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.people),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Insira o número de porções';
                                }
                                if (int.tryParse(value) == null) {
                                  return 'Insira um número válido';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _tempoPreparoController,
                              decoration: const InputDecoration(
                                labelText: 'Tempo Preparo (min)',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.timer),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Insira o tempo de preparo';
                                }
                                if (int.tryParse(value) == null) {
                                  return 'Insira um número válido';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: _dificuldadeSelecionada,
                        decoration: const InputDecoration(
                          labelText: 'Dificuldade',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.leaderboard),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'FACIL', child: Text('Fácil')),
                          DropdownMenuItem(value: 'MEDIO', child: Text('Médio')),
                          DropdownMenuItem(value: 'DIFICIL', child: Text('Difícil')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _dificuldadeSelecionada = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Selecione a dificuldade';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _imagemUrlController,
                        decoration: const InputDecoration(
                          labelText: 'URL da Imagem',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.image),
                        ),
                        validator: (value) {
                          // Campo opcional
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // Botão de login
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _guardarReceita,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Guadar',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
