import 'ingrediente.dart';

// Modelo de dados para uma receita culinária
// Representa todas as informações necessárias para preparar um prato
class Receita {
  final String? id;
  final String titulo;
  final String descricao;
  final List<Ingrediente> ingredientes;
  final String instrucoes;
  final String? imagemUrl;
  final int? tempoPreparo; // em minutos
  final int? numeroPorcoes;
  final String? dificuldade; // FACIL, MEDIO, DIFICIL

  // Construtor da classe
  // Os campos obrigatórios são título, descrição, ingredientes e instruções
  const Receita({
    this.id,
    required this.titulo,
    required this.descricao,
    required this.ingredientes,
    required this.instrucoes,
    this.imagemUrl,
    this.tempoPreparo,
    this.numeroPorcoes,
    this.dificuldade,
  });

  // Cria uma cópia da receita com determinados campos alterados
  // Útil para actualizar apenas alguns atributos mantendo os restantes inalterados
  Receita copyWith({
    String? id,
    String? titulo,
    String? descricao,
    List<Ingrediente>? ingredientes,
    String? instrucoes,
    String? imagemUrl,
    int? tempoPreparo,
    int? numeroPorcoes,
    String? dificuldade,
  }) {
    return Receita(
      id: id ?? this.id,
      titulo: titulo ?? this.titulo,
      descricao: descricao ?? this.descricao,
      ingredientes: ingredientes ?? this.ingredientes,
      instrucoes: instrucoes ?? this.instrucoes,
      imagemUrl: imagemUrl ?? this.imagemUrl,
      tempoPreparo: tempoPreparo ?? this.tempoPreparo,
      numeroPorcoes: numeroPorcoes ?? this.numeroPorcoes,
      dificuldade: dificuldade ?? this.dificuldade,
    );
  }

  // Converte a receita para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'titulo': titulo,
      'descricao': descricao,
      'ingredientes': ingredientes.map((ingrediente) => ingrediente.toMap()).toList(),
      'instrucoes': instrucoes,
      'imagemUrl': imagemUrl,
      'tempoPreparo': tempoPreparo,
      'numeroPorcoes': numeroPorcoes,
      'dificuldade': dificuldade,
    };
  }

  // Converte a receita para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'titulo': titulo,
      'descricao': descricao,
      'instrucoes': instrucoes,
      'tempoPreparo': tempoPreparo,
      'numeroPorcoes': numeroPorcoes,
      'dificuldade': dificuldade,
      'imagemUrl': imagemUrl,
      'ingredientes': ingredientes.map((ingrediente) => ingrediente.toJson()).toList(),
    };
  }

  // Cria uma receita a partir de um Map (armazenamento local)
  factory Receita.fromMap(Map<String, dynamic> map) {
    return Receita(
      id: map['id']?.toString(),
      titulo: map['titulo'] ?? '',
      descricao: map['descricao'] ?? '',
      ingredientes: List<Ingrediente>.from(
        map['ingredientes']?.map((x) => Ingrediente.fromMap(x)) ?? [],
      ),
      instrucoes: map['instrucoes'] ?? map['modoPreparacao'] ?? '',
      imagemUrl: map['imagemUrl'],
      tempoPreparo: map['tempoPreparo'],
      numeroPorcoes: map['numeroPorcoes'],
      dificuldade: map['dificuldade'],
    );
  }

  // Cria uma receita a partir de JSON (API)
  factory Receita.fromJson(Map<String, dynamic> json) {
    return Receita(
      id: json['id']?.toString(),
      titulo: json['titulo'] ?? '',
      descricao: json['descricao'] ?? '',
      instrucoes: json['instrucoes'] ?? '',
      tempoPreparo: json['tempoPreparo'],
      numeroPorcoes: json['numeroPorcoes'],
      dificuldade: json['dificuldade'],
      imagemUrl: json['imagemUrl'],
      ingredientes: List<Ingrediente>.from(
        json['ingredientes']?.map((x) => Ingrediente.fromJson(x)) ?? [],
      ),
    );
  }

  @override
  String toString() {
    return 'Receita(id: $id, titulo: $titulo, tempoPreparo: $tempoPreparo, numeroPorcoes: $numeroPorcoes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Receita &&
      other.id == id &&
      other.titulo == titulo &&
      other.descricao == descricao &&
      other.ingredientes == ingredientes &&
      other.instrucoes == instrucoes &&
      other.imagemUrl == imagemUrl &&
      other.tempoPreparo == tempoPreparo &&
      other.numeroPorcoes == numeroPorcoes &&
      other.dificuldade == dificuldade;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      titulo.hashCode ^
      descricao.hashCode ^
      ingredientes.hashCode ^
      instrucoes.hashCode ^
      imagemUrl.hashCode ^
      tempoPreparo.hashCode ^
      numeroPorcoes.hashCode ^
      dificuldade.hashCode;
  }
}
