# 🎓 Perguntas Avançadas - EST Receitas

## 🏗️ Decisões Arquiteturais

### **Escolhas de Design**
1. **Por que foi escolhida uma arquitetura híbrida em vez de apenas online ou offline?**
   - R: Garante funcionalidade offline (UX), sincronização quando possível (dados atualizados), melhor performance (cache local)

2. **Qual a justificação para usar SharedPreferences em vez de SQLite?**
   - R: Simplicidade para dados estruturados pequenos, serialização JSON automática, menos overhead

3. **Por que não foi usado um state management como BLoC ou Provider?**
   - R: Projeto pequeno/médio, StreamController suficiente, menos dependências, aprendizagem gradual

4. **Como foi decidida a estrutura de pastas do projeto?**
   - R: Separação por responsabilidade (models, services, screens), escalabilidade, manutenibilidade

### **Padrões Implementados**
5. **Explique o padrão Repository implementado nos serviços híbridos.**
   - R: Abstração da fonte de dados, interface única para local+remoto, facilita testes e manutenção

6. **Como funciona o padrão Factory nos modelos de dados?**
   - R: Factory constructors (fromJson, fromMap) para criação tipada, validação centralizada

7. **Que padrão de error handling foi implementado?**
   - R: Result pattern com ResultadoAutenticacao, encapsula sucesso/erro, type-safe

8. **Como é implementado o padrão Observer nos streams?**
   - R: StreamController.broadcast(), múltiplos listeners, notificação automática de mudanças

---

## 💾 Gestão de Dados

### **Estratégias de Persistência**
9. **Como é garantida a integridade dos dados entre local e remoto?**
   - R: Validação em ambas as camadas, timestamps para conflitos, rollback em caso de erro

10. **Que estratégia de cache invalidation é usada?**
    - R: Time-based (básico), pode ser melhorado com ETags, versioning

11. **Como são tratadas as migrações de dados locais?**
    - R: Atualmente não implementado, seria necessário versioning do schema

12. **Explique a estratégia de conflict resolution implementada.**
    - R: Last-write-wins simples, pode ser melhorado com merge strategies

### **Sincronização**
13. **Como funciona a sincronização bidirecional de dados?**
    - R: Upload de mudanças locais, download de mudanças remotas, merge com estratégia definida

14. **Que mecanismo de queue é usado para operações offline?**
    - R: Atualmente não implementado, seria necessário para robustez

15. **Como é detectada a necessidade de sincronização?**
    - R: Tentativa em cada operação, pode ser melhorado com background sync

---

## 🔐 Segurança e Autenticação

### **Gestão de Tokens**
16. **Como é implementado o refresh de tokens JWT?**
    - R: Atualmente não implementado, tokens têm validade fixa

17. **Onde são armazenados os tokens de forma segura?**
    - R: SharedPreferences (básico), deveria usar secure storage

18. **Como é implementada a invalidação de sessões?**
    - R: Logout remove token local, backend pode invalidar centralmente

19. **Que estratégia de rate limiting seria implementada?**
    - R: Backend: sliding window, Frontend: debouncing de requests

### **Validação e Sanitização**
20. **Como são validados os inputs do utilizador?**
    - R: Frontend: validators em formulários, Backend: Bean Validation

21. **Que medidas contra ataques comuns estão implementadas?**
    - R: SQL injection (JPA), XSS (sanitização), CSRF (stateless JWT)

22. **Como é implementada a auditoria de ações?**
    - R: Logs básicos, pode ser melhorado com audit trail completo

---

## 🚀 Performance e Otimização

### **Frontend Performance**
23. **Como é otimizado o carregamento inicial da aplicação?**
    - R: Splash screen, lazy loading de dados, cache de recursos

24. **Que estratégias de lazy loading são implementadas?**
    - R: Básico em listas, pode ser melhorado com pagination

25. **Como é gerida a memória em listas grandes?**
    - R: ListView.builder para rendering eficiente, dispose de controllers

26. **Que otimizações de imagens são implementadas?**
    - R: Atualmente básico, pode adicionar cache, compression, thumbnails

### **Backend Performance**
27. **Como são otimizadas as queries de base de dados?**
    - R: JPA queries eficientes, índices apropriados, lazy loading

28. **Que estratégia de connection pooling é usada?**
    - R: HikariCP (padrão Spring Boot), configuração otimizada

29. **Como é implementado o caching no backend?**
    - R: @Cacheable do Spring, cache de segundo nível Hibernate

---

## 🧪 Testabilidade e Qualidade

### **Arquitetura para Testes**
30. **Como a arquitetura facilita os testes unitários?**
    - R: Injeção de dependências, interfaces abstratas, mocking fácil

31. **Que estratégia de mocking é usada nos testes?**
    - R: Mockito para backend, mockito/flutter_test para frontend

32. **Como são testadas as integrações com APIs externas?**
    - R: WireMock para mock servers, contract testing

33. **Que abordagem de TDD seria implementada?**
    - R: Red-Green-Refactor, testes primeiro para lógica crítica

### **Qualidade de Código**
34. **Como é garantida a consistência do código?**
    - R: Linting rules, code formatting automático, code reviews

35. **Que métricas de complexidade são monitorizadas?**
    - R: Cyclomatic complexity, cognitive complexity, code coverage

36. **Como é implementada a documentação automática?**
    - R: Dartdoc para Flutter, Javadoc para backend, OpenAPI para APIs

---

## 🔄 Integração e APIs

### **Design de APIs**
37. **Como é implementado o versionamento da API?**
    - R: URL versioning (/api/v1/), backward compatibility

38. **Que estratégia de pagination é usada?**
    - R: Offset-based básico, pode melhorar com cursor-based

39. **Como são implementados os filtros e pesquisas?**
    - R: Query parameters, Specification pattern para queries dinâmicas

40. **Que formato de response é padronizado?**
    - R: JSON consistente, envelope pattern para metadados

### **Integração com Serviços Externos**
41. **Como seria implementada integração com serviços de pagamento?**
    - R: Adapter pattern, webhook handling, idempotency

42. **Como seria implementada integração com APIs de nutrição?**
    - R: HTTP clients, circuit breaker, fallback data

43. **Que estratégia de webhook seria implementada?**
    - R: Async processing, retry mechanism, signature validation

---

## 📱 Mobile e Multiplataforma

### **Especificidades Mobile**
44. **Como é tratada a rotação de ecrã?**
    - R: Responsive widgets, orientation locks onde necessário

45. **Como são implementadas as notificações push?**
    - R: Firebase Cloud Messaging, local notifications

46. **Que estratégias de background processing são usadas?**
    - R: Isolates para tarefas pesadas, background sync

47. **Como é implementado o deep linking?**
    - R: Named routes, URL parsing, state restoration

### **Multiplataforma**
48. **Como são tratadas as diferenças entre plataformas?**
    - R: Platform.isAndroid/isIOS, conditional imports

49. **Que adaptações são feitas para web?**
    - R: Responsive design, web-specific widgets, PWA features

50. **Como é implementado o suporte offline em web?**
    - R: Service workers, IndexedDB, cache strategies

---

## 🔮 Escalabilidade e Futuro

### **Arquitetura Escalável**
51. **Como a aplicação seria migrada para microservices?**
    - R: Domain-driven design, API gateway, service mesh

52. **Que estratégia de database sharding seria implementada?**
    - R: Horizontal partitioning por utilizador, consistent hashing

53. **Como seria implementado o multi-tenancy?**
    - R: Schema isolation, tenant context, data segregation

54. **Que padrões de resilience seriam implementados?**
    - R: Circuit breaker, bulkhead, timeout, retry with backoff

### **Evolução Tecnológica**
55. **Como seria a migração para Flutter 4.0+?**
    - R: Gradual migration, deprecation handling, testing

56. **Que tecnologias emergentes poderiam ser integradas?**
    - R: AI/ML para sugestões, GraphQL para APIs, WebAssembly

57. **Como seria implementada a internacionalização?**
    - R: flutter_localizations, ARB files, context-aware translations

---

## 🎯 Casos de Uso Complexos

### **Cenários Avançados**
58. **Como seria implementada a colaboração em tempo real?**
    - R: WebSockets, operational transforms, conflict resolution

59. **Como seria implementado um sistema de recomendações?**
    - R: Machine learning, collaborative filtering, content-based

60. **Como seria implementada a sincronização entre dispositivos?**
    - R: Cloud sync, device registration, conflict resolution

### **Integração com IoT**
61. **Como seria integrada com dispositivos de cozinha inteligentes?**
    - R: MQTT, device APIs, real-time monitoring

62. **Como seria implementado o controlo por voz?**
    - R: Speech recognition, natural language processing, voice commands

63. **Que estratégias de edge computing seriam usadas?**
    - R: Local processing, edge servers, data synchronization

---

## 🛡️ Segurança Avançada

### **Proteção de Dados**
64. **Como seria implementada a criptografia end-to-end?**
    - R: Client-side encryption, key management, secure protocols

65. **Que estratégias de data masking seriam implementadas?**
    - R: PII protection, anonymization, pseudonymization

66. **Como seria implementado o compliance com GDPR?**
    - R: Data portability, right to erasure, consent management

### **Auditoria e Monitorização**
67. **Como seria implementado um sistema de auditoria completo?**
    - R: Event sourcing, immutable logs, compliance reporting

68. **Que estratégias de threat detection seriam implementadas?**
    - R: Anomaly detection, behavioral analysis, real-time alerts

69. **Como seria implementada a forensics digital?**
    - R: Immutable logs, chain of custody, evidence preservation

---

## 🎨 UX/UI Avançado

### **Experiência do Utilizador**
70. **Como seria implementada a personalização da interface?**
    - R: Theme customization, layout preferences, adaptive UI

71. **Que estratégias de acessibilidade seriam implementadas?**
    - R: Screen reader support, high contrast, voice navigation

72. **Como seria implementada a análise de comportamento do utilizador?**
    - R: Analytics integration, heatmaps, A/B testing

### **Interface Adaptativa**
73. **Como seria implementada uma UI que se adapta ao contexto?**
    - R: Context awareness, adaptive layouts, smart defaults

74. **Que estratégias de progressive disclosure seriam usadas?**
    - R: Layered information, contextual help, guided tours

75. **Como seria implementada a interface conversacional?**
    - R: Chatbot integration, natural language interface, voice UI

---

*Estas perguntas avançadas exploram conceitos mais profundos de arquitetura, design patterns, e implementações complexas que demonstram conhecimento técnico sólido e visão estratégica do desenvolvimento de software.*
