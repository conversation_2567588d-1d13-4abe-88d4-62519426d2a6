package com.estrecitas.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/testes")
public class TestController {

    @GetMapping("/saude")
    public ResponseEntity<Map<String, Object>> verificarSaude() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Est Receitas Backend está a funcionar!");
        response.put("timestamp", LocalDateTime.now());
        response.put("version", "1.0.0");
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/cors")
    public ResponseEntity<Map<String, String>> corsTest() {
        Map<String, String> response = new HashMap<>();
        response.put("message", "CORS está configurado corretamente!");
        response.put("origin", "Backend Spring Boot");
        
        return ResponseEntity.ok(response);
    }
}
