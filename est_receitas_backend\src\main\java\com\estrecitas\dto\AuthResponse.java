package com.estrecitas.dto;

/**
 * DTO (Data Transfer Object) para respostas de autenticação
 *
 * Esta classe padroniza as respostas dos endpoints de autenticação,
 * incluindo login e registo. Contém:
 * - Token JWT para autenticação subsequente
 * - Dados básicos do utilizador autenticado
 * - Indicadores de sucesso/erro
 * - Mensagens informativas
 *
 * Usado pelos endpoints:
 * - POST /api/autenticacao/login
 * - POST /api/autenticacao/registo
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
public class AuthResponse {

    /**
     * Token JWT para autenticação
     * Null em caso de erro
     */
    private String token;

    /**
     * Tipo do token (sempre "Bearer" para JWT)
     */
    private String type = "Bearer";

    /**
     * ID único do utilizador autenticado
     */
    private Long userId;

    /**
     * Nome completo do utilizador
     */
    private String nome;

    /**
     * Email do utilizador
     */
    private String email;

    /**
     * Indica se a operação foi bem-sucedida
     */
    private boolean success;

    /**
     * Mensagem descritiva do resultado
     */
    private String message;
    
    // Construtores
    public AuthResponse() {}
    
    public AuthResponse(String token, Long userId, String nome, String email) {
        this.token = token;
        this.userId = userId;
        this.nome = nome;
        this.email = email;
        this.success = true;
        this.message = "Autenticação realizada com sucesso";
    }
    
    public AuthResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    // Métodos estáticos para criar respostas
    public static AuthResponse success(String token, Long userId, String nome, String email) {
        return new AuthResponse(token, userId, nome, email);
    }
    
    public static AuthResponse error(String message) {
        return new AuthResponse(false, message);
    }
    
    // Getters e Setters
    public String getToken() {
        return token;
    }
    
    public void setToken(String token) {
        this.token = token;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    

    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    @Override
    public String toString() {
        return "AuthResponse{" +
                "type='" + type + '\'' +
                ", userId=" + userId +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                ", success=" + success +
                ", message='" + message + '\'' +
                '}';
    }
}
