package com.estrecitas.model;

/**
 * Enum que define os tipos de armazenamento disponíveis no sistema.
 * Representa os locais onde os itens de stock podem ser guardados.
 */
public enum TipoArmazenamento {
    DESPENSA("Despensa"),
    FRIGORIFICO("Frigorífico");
    
    private final String descricao;
    
    /**
     * Construtor do enum TipoArmazenamento.
     * 
     * @param descricao Descrição legível do tipo de armazenamento
     */
    TipoArmazenamento(String descricao) {
        this.descricao = descricao;
    }
    
    /**
     * Obtém a descrição do tipo de armazenamento.
     * 
     * @return Descrição legível do tipo de armazenamento
     */
    public String getDescricao() {
        return descricao;
    }
    
    /**
     * Retorna a descrição do tipo de armazenamento como string.
     * 
     * @return Descrição do tipo de armazenamento
     */
    @Override
    public String toString() {
        return descricao;
    }
}
