package com.estrecitas.service;

import com.estrecitas.dto.StockItemDTO;
import com.estrecitas.mapper.DTOMapper;
import com.estrecitas.model.StockItem;
import com.estrecitas.model.TipoArmazenamento;
import com.estrecitas.repository.StockItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class StockService {
    
    @Autowired
    private StockItemRepository stockItemRepository;
    
    @Autowired
    private DTOMapper dtoMapper;
    
    // ===== OPERAÇÕES BÁSICAS =====
    
    public List<StockItemDTO> obterTodosItens() {
        List<StockItem> itens = stockItemRepository.findAll();
        return dtoMapper.toStockItemDTOList(itens);
    }
    
    public Optional<StockItemDTO> obterItemPorId(Long id) {
        Optional<StockItem> item = stockItemRepository.findById(id);
        return item.map(dtoMapper::toStockItemDTO);
    }
    
    public StockItemDTO adicionarItem(StockItemDTO stockItemDTO) {
        validarStockItem(stockItemDTO);
        
        StockItem stockItem = dtoMapper.toStockItem(stockItemDTO);
        StockItem itemGuardado = stockItemRepository.save(stockItem);
        
        return dtoMapper.toStockItemDTO(itemGuardado);
    }
    
    public StockItemDTO atualizarItem(Long id, StockItemDTO stockItemDTO) {
        Optional<StockItem> itemExistente = stockItemRepository.findById(id);
        
        if (itemExistente.isPresent()) {
            StockItem stockItem = itemExistente.get();
            dtoMapper.updateStockItemFromDTO(stockItem, stockItemDTO);
            
            StockItem itemAtualizado = stockItemRepository.save(stockItem);
            return dtoMapper.toStockItemDTO(itemAtualizado);
        }
        
        return null;
    }
    
    public StockItemDTO atualizarQuantidade(Long id, Double novaQuantidade) {
        Optional<StockItem> itemExistente = stockItemRepository.findById(id);
        
        if (itemExistente.isPresent()) {
            StockItem stockItem = itemExistente.get();
            stockItem.setQuantidade(novaQuantidade);
            
            StockItem itemAtualizado = stockItemRepository.save(stockItem);
            return dtoMapper.toStockItemDTO(itemAtualizado);
        }
        
        return null;
    }
    
    public StockItemDTO atualizarValidade(Long id, LocalDate novaDataValidade) {
        Optional<StockItem> itemExistente = stockItemRepository.findById(id);
        
        if (itemExistente.isPresent()) {
            StockItem stockItem = itemExistente.get();
            stockItem.setDataValidade(novaDataValidade);
            
            StockItem itemAtualizado = stockItemRepository.save(stockItem);
            return dtoMapper.toStockItemDTO(itemAtualizado);
        }
        
        return null;
    }
    
    public boolean removerItem(Long id) {
        if (stockItemRepository.existsById(id)) {
            stockItemRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // ===== OPERAÇÕES POR LOCALIZAÇÃO =====
    
    public List<StockItemDTO> obterItensDespensa() {
        List<StockItem> itens = stockItemRepository.findDespensaItems();
        return dtoMapper.toStockItemDTOList(itens);
    }
    
    public List<StockItemDTO> obterItensFrigorifico() {
        List<StockItem> itens = stockItemRepository.findFrigorificoItems();
        return dtoMapper.toStockItemDTOList(itens);
    }
    

    
    // ===== OPERAÇÕES DE PESQUISA =====
    
    public List<StockItemDTO> pesquisarItensPorNome(String nome) {
        List<StockItem> itens = stockItemRepository.findByNomeContainingIgnoreCase(nome);
        return dtoMapper.toStockItemDTOList(itens);
    }
    

    

    

    
    public List<StockItemDTO> obterItensPorQuantidadeMinima(Double quantidadeMinima) {
        List<StockItem> itens = stockItemRepository.findByQuantidadeGreaterThanEqual(quantidadeMinima);
        return dtoMapper.toStockItemDTOList(itens);
    }
    
    public List<StockItemDTO> obterItensPorQuantidadeMaxima(Double quantidadeMaxima) {
        List<StockItem> itens = stockItemRepository.findByQuantidadeLessThanEqual(quantidadeMaxima);
        return dtoMapper.toStockItemDTOList(itens);
    }
    
    // ===== OPERAÇÕES AUXILIARES =====
    

    
    public List<String> obterTodasUnidades() {
        return stockItemRepository.findDistinctUnidades();
    }

    public long contarItens() {
        return stockItemRepository.count();
    }
    
    public long contarItensPorLocalizacao(TipoArmazenamento localizacao) {
        return stockItemRepository.findByLocalizacao(localizacao).size();
    }
    
    // ===== VALIDAÇÕES =====
    
    public boolean existeItem(Long id) {
        return stockItemRepository.existsById(id);
    }
    
    public boolean existeItemComNome(String nome) {
        return stockItemRepository.existsByNomeIgnoreCase(nome);
    }
    
    private void validarStockItem(StockItemDTO stockItemDTO) {
        if (stockItemDTO.getNome() == null || stockItemDTO.getNome().trim().isEmpty()) {
            throw new IllegalArgumentException("Nome do item é obrigatório");
        }
        
        if (stockItemDTO.getQuantidade() == null || stockItemDTO.getQuantidade() < 0) {
            throw new IllegalArgumentException("Quantidade deve ser um valor positivo");
        }
        
        if (stockItemDTO.getUnidade() == null || stockItemDTO.getUnidade().trim().isEmpty()) {
            throw new IllegalArgumentException("Unidade é obrigatória");
        }
        
        if (stockItemDTO.getLocalizacao() == null) {
            throw new IllegalArgumentException("Localização é obrigatória");
        }
        
        if (stockItemDTO.getDataValidade() != null && stockItemDTO.getDataValidade().isBefore(LocalDate.now().minusYears(1))) {
            throw new IllegalArgumentException("Data de validade não pode ser muito antiga");
        }
    }
}
