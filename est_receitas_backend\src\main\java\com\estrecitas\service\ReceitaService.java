package com.estrecitas.service;

import com.estrecitas.dto.IngredienteDTO;
import com.estrecitas.dto.ReceitaDTO;
import com.estrecitas.mapper.DTOMapper;
import com.estrecitas.model.Ingrediente;
import com.estrecitas.model.Receita;
import com.estrecitas.model.DificuldadeReceita;
import com.estrecitas.repository.ReceitaRepository;
import com.estrecitas.repository.IngredienteRepository;
import com.estrecitas.repository.StockItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ReceitaService {
    
    @Autowired
    private ReceitaRepository receitaRepository;
    
    @Autowired
    private IngredienteRepository ingredienteRepository;
    
    @Autowired
    private StockItemRepository stockItemRepository;
    
    @Autowired
    private DTOMapper dtoMapper;
    
    // ===== OPERAÇÕES BÁSICAS =====
    
    public List<ReceitaDTO> obterTodasReceitas() {
        List<Receita> receitas = receitaRepository.findAll();
        return dtoMapper.toReceitaDTOList(receitas);
    }
    
    public Optional<ReceitaDTO> obterReceitaPorId(Long id) {
        Optional<Receita> receita = receitaRepository.findById(id);
        return receita.map(dtoMapper::toReceitaDTO);
    }
    
    public ReceitaDTO guardarReceita(ReceitaDTO receitaDTO) {
        Receita receita = dtoMapper.toReceita(receitaDTO);
        
        // Guardar a receita primeiro
        Receita receitaGuardada = receitaRepository.save(receita);
        
        // Processar ingredientes se existirem
        if (receitaDTO.getIngredientes() != null && !receitaDTO.getIngredientes().isEmpty()) {
            for (IngredienteDTO ingredienteDTO : receitaDTO.getIngredientes()) {
                Ingrediente ingrediente = dtoMapper.toIngrediente(ingredienteDTO);
                ingrediente.setReceita(receitaGuardada);
                ingredienteRepository.save(ingrediente);
            }
            
            // Recarregar a receita com os ingredientes
            receitaGuardada = receitaRepository.findById(receitaGuardada.getId()).orElse(receitaGuardada);
        }
        
        return dtoMapper.toReceitaDTO(receitaGuardada);
    }
    
    public ReceitaDTO atualizarReceita(Long id, ReceitaDTO receitaDTO) {
        Optional<Receita> receitaExistente = receitaRepository.findById(id);
        
        if (receitaExistente.isPresent()) {
            Receita receita = receitaExistente.get();
            dtoMapper.updateReceitaFromDTO(receita, receitaDTO);
            
            // Atualizar ingredientes
            if (receitaDTO.getIngredientes() != null) {
                // Remover ingredientes existentes
                ingredienteRepository.deleteAll(receita.getIngredientes());
                receita.getIngredientes().clear();
                
                // Adicionar novos ingredientes
                for (IngredienteDTO ingredienteDTO : receitaDTO.getIngredientes()) {
                    Ingrediente ingrediente = dtoMapper.toIngrediente(ingredienteDTO);
                    ingrediente.setReceita(receita);
                    receita.adicionarIngrediente(ingrediente);
                }
            }
            
            Receita receitaAtualizada = receitaRepository.save(receita);
            return dtoMapper.toReceitaDTO(receitaAtualizada);
        }
        
        return null;
    }
    
    public boolean eliminarReceita(Long id) {
        if (receitaRepository.existsById(id)) {
            receitaRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    // ===== OPERAÇÕES DE PESQUISA =====
    
    public List<ReceitaDTO> pesquisarReceitasPorTitulo(String titulo) {
        List<Receita> receitas = receitaRepository.findByTituloContainingIgnoreCase(titulo);
        return dtoMapper.toReceitaDTOList(receitas);
    }
    
    public List<ReceitaDTO> obterReceitasPorCategoria(String categoria) {
        List<Receita> receitas = receitaRepository.findByCategoriaIgnoreCase(categoria);
        return dtoMapper.toReceitaDTOList(receitas);
    }

    
    public List<ReceitaDTO> obterReceitasPorTempoMaximo(Integer tempoMaximo) {
        List<Receita> receitas = receitaRepository.findByTempoPreparoLessThanEqual(tempoMaximo);
        return dtoMapper.toReceitaDTOList(receitas);
    }
    

    
    public List<ReceitaDTO> pesquisarReceitasAvancada(String titulo, String categoria, 
                                                     DificuldadeReceita dificuldade, Integer tempoMaximo) {
        List<Receita> receitas = receitaRepository.findByMultiplosCriterios(titulo, categoria, dificuldade, tempoMaximo);
        return dtoMapper.toReceitaDTOList(receitas);
    }
    
    // ===== SUGESTÕES BASEADAS NO STOCK =====
    
    public List<ReceitaDTO> sugerirReceitasComBaseNoStock() {
        // Obter ingredientes disponíveis no stock
        List<String> ingredientesDisponiveis = stockItemRepository.findNomesIngredientesDisponiveis();
        
        if (ingredientesDisponiveis.isEmpty()) {
            return List.of();
        }
        
        // Converter para lowercase para comparação
        List<String> ingredientesLowerCase = ingredientesDisponiveis.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toList());
        
        // Buscar receitas que contenham estes ingredientes
        List<Object[]> resultados = receitaRepository.findReceitasComIngredientesDisponiveis(ingredientesLowerCase);
        
        // Extrair as receitas dos resultados
        List<Receita> receitas = resultados.stream()
                .map(resultado -> (Receita) resultado[0])
                .distinct()
                .collect(Collectors.toList());
        
        return dtoMapper.toReceitaDTOList(receitas);
    }
    

    
    // ===== OPERAÇÕES AUXILIARES =====
    
    public List<String> obterTodasCategorias() {
        return receitaRepository.findDistinctCategorias();
    }
    
    public List<ReceitaDTO> obterReceitasRecentes() {
        List<Receita> receitas = receitaRepository.findReceitasRecentes();
        return dtoMapper.toReceitaDTOList(receitas);
    }
    
    public long contarReceitas() {
        return receitaRepository.count();
    }
    
    // ===== VALIDAÇÕES =====
    
    public boolean existeReceita(Long id) {
        return receitaRepository.existsById(id);
    }
    
    private void validarReceita(ReceitaDTO receitaDTO) {
        if (receitaDTO.getTitulo() == null || receitaDTO.getTitulo().trim().isEmpty()) {
            throw new IllegalArgumentException("Título da receita é obrigatório");
        }
        
        if (receitaDTO.getInstrucoes() == null || receitaDTO.getInstrucoes().trim().isEmpty()) {
            throw new IllegalArgumentException("Instruções da receita são obrigatórias");
        }
        
        if (receitaDTO.getIngredientes() == null || receitaDTO.getIngredientes().isEmpty()) {
            throw new IllegalArgumentException("Receita deve ter pelo menos um ingrediente");
        }
    }
}
