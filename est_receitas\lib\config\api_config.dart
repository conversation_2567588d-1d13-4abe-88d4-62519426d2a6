class ApiConfig {
  // URL base da API (localhost para desenvolvimento)
  static const String baseUrl = 'http://localhost:8080/api';

  // URLs específicas para cada endpoint
  static const String receitasUrl = '$baseUrl/receitas';
  static const String stockUrl = '$baseUrl/stock';
  static const String testesUrl = '$baseUrl/testes';
  static const String autenticacaoUrl = '$baseUrl/autenticacao'; // Corrigido aqui

  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 10);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // Headers padrão
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers para requests com dados
  static const Map<String, String> jsonHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Configurações de retry
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Configurações para diferentes ambientes
  static String getBaseUrlForEnvironment(String environment) {
    switch (environment.toLowerCase()) {
      case 'development':
      case 'dev':
        return 'http://localhost:8080/api';
      case 'staging':
        return 'http://staging-api.estrecitas.com/api';
      case 'production':
      case 'prod':
        return 'https://api.estrecitas.com/api';
      default:
        return 'http://localhost:8080/api';
    }
  }

  // Método para verificar se a API está disponível
  static bool isApiAvailable() {
    // Esta lógica pode ser expandida para verificar conectividade
    return true;
  }

  // URLs específicas para endpoints de receitas
  static String get receitasPesquisarUrl => '$receitasUrl/pesquisar';
  static String get receitasCategoriasUrl => '$receitasUrl/categorias';
  static String get receitasRecentesUrl => '$receitasUrl/recentes';
  static String get receitasEstatisticasUrl => '$receitasUrl/estatisticas';

  // URLs específicas para endpoints de stock
  static String get stockDespensaUrl => '$stockUrl/despensa';
  static String get stockFrigorificoUrl => '$stockUrl/frigorifico';
  static String get stockPesquisarUrl => '$stockUrl/pesquisar';
  static String get stockVencidosUrl => '$stockUrl/vencidos';

  // URLs para testes
  static String get verificarSaudeUrl => '$testesUrl/saude';
  static String get corsTestUrl => '$testesUrl/cors';

  // URLs para autenticação
  static String get registoUrl => '$autenticacaoUrl/registo';
  static String get loginUrl => '$autenticacaoUrl/login';
  static String get verificarTokenUrl => '$autenticacaoUrl/verificar';
  static String get perfilUrl => '$autenticacaoUrl/perfil';
  static String get verificarEmailUrl => '$autenticacaoUrl/verificar-email';
  static String get estatisticasUrl => '$autenticacaoUrl/stats';
  static String get logoutUrl => '$autenticacaoUrl/logout';

  // Métodos auxiliares para construir URLs
  static String receitaPorId(int id) => '$receitasUrl/$id';
  static String stockItemPorId(int id) => '$stockUrl/$id';
  static String receitasPorCategoria(String categoria) => '$receitasUrl/categoria/$categoria';

  // Parâmetros de query comuns
  static String buildQueryParams(Map<String, dynamic> params) {
    if (params.isEmpty) return '';
    
    final queryParams = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
    
    return queryParams.isNotEmpty ? '?$queryParams' : '';
  }

  // Método para construir URL com parâmetros
  static String buildUrl(String baseUrl, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) return baseUrl;
    return baseUrl + buildQueryParams(params);
  }

  // Configurações de logging (para debug)
  static const bool enableLogging = true;
  static const bool logRequestBody = true;
  static const bool logResponseBody = true;

  // Configurações de cache
  static const Duration cacheTimeout = Duration(minutes: 5);
  static const bool enableCache = false; // Desabilitado por enquanto

  // Configurações de segurança
  static const bool validateCertificates = true;
  static const bool allowSelfSignedCertificates = false; // Apenas para desenvolvimento
}
