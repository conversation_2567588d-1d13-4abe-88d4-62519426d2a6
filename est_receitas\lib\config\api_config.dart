class ApiConfig {
  // URL base da API
  static const String baseUrl = 'http://localhost:8080/api';

  // URLs para cada endpoint
  static const String receitasUrl = '$baseUrl/receitas';
  static const String stockUrl = '$baseUrl/stock';
  static const String testesUrl = '$baseUrl/testes';
  static const String autenticacaoUrl = '$baseUrl/autenticacao';

  // cabeçalhos HTTP padrão
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // cabeçalhos HTTP para requests com dados (mesmo que defaultHeaders)
  static const Map<String, String> jsonHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // URLs específicas para endpoints de receitas
  static String get receitasPesquisarUrl => '$receitasUrl/pesquisar';

  // URLs específicas para endpoints de stock
  static String get stockDespensaUrl => '$stockUrl/despensa';
  static String get stockFrigorificoUrl => '$stockUrl/frigorifico';
  static String get stockPesquisarUrl => '$stockUrl/pesquisar';
  static String get stockVencidosUrl => '$stockUrl/vencidos';

  // URLs para autenticação
  static String get registoUrl => '$autenticacaoUrl/registo';
  static String get loginUrl => '$autenticacaoUrl/login';
  static String get verificarTokenUrl => '$autenticacaoUrl/verificar';
  static String get perfilUrl => '$autenticacaoUrl/perfil';
  static String get verificarEmailUrl => '$autenticacaoUrl/verificar-email';
  static String get logoutUrl => '$autenticacaoUrl/logout';

  // Métodos auxiliares para construir URLs
  static String receitaPorId(int id) => '$receitasUrl/$id';
  static String stockItemPorId(int id) => '$stockUrl/$id';

  // Parâmetros de query comuns
  static String buildQueryParams(Map<String, dynamic> params) {
    if (params.isEmpty) return '';
    
    final queryParams = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
    
    return queryParams.isNotEmpty ? '?$queryParams' : '';
  }

  // Método para construir URL com parâmetros
  static String buildUrl(String baseUrl, Map<String, dynamic>? params) {
    if (params == null || params.isEmpty) return baseUrl;
    return baseUrl + buildQueryParams(params);
  }

  // Configurações de logging
  static const bool enableLogging = true;
  static const bool logRequestBody = true;
}