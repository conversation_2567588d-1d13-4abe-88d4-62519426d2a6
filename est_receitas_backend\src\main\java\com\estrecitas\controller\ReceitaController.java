package com.estrecitas.controller;

import com.estrecitas.dto.ReceitaDTO;
import com.estrecitas.model.DificuldadeReceita;
import com.estrecitas.service.ReceitaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/receitas")
@CrossOrigin(origins = "*")
public class ReceitaController {
    
    @Autowired
    private ReceitaService receitaService;
    
    // ===== OPERAÇÕES BÁSICAS =====
    
    @GetMapping
    public ResponseEntity<List<ReceitaDTO>> obterTodasReceitas() {
        try {
            List<ReceitaDTO> receitas = receitaService.obterTodasReceitas();
            return ResponseEntity.ok(receitas);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ReceitaDTO> obterReceitaPorId(@PathVariable Long id) {
        try {
            Optional<ReceitaDTO> receita = receitaService.obterReceitaPorId(id);
            return receita.map(ResponseEntity::ok)
                         .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PostMapping
    public ResponseEntity<ReceitaDTO> criarReceita(@RequestBody ReceitaDTO receitaDTO) {
        try {
            ReceitaDTO novaReceita = receitaService.guardarReceita(receitaDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(novaReceita);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ReceitaDTO> atualizarReceita(@PathVariable Long id, @RequestBody ReceitaDTO receitaDTO) {
        try {
            ReceitaDTO receitaAtualizada = receitaService.atualizarReceita(id, receitaDTO);
            if (receitaAtualizada != null) {
                return ResponseEntity.ok(receitaAtualizada);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> eliminarReceita(@PathVariable Long id) {
        try {
            boolean eliminada = receitaService.eliminarReceita(id);
            if (eliminada) {
                return ResponseEntity.noContent().build();
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // ===== OPERAÇÕES DE PESQUISA =====
    
    @GetMapping("/pesquisar")
    public ResponseEntity<List<ReceitaDTO>> pesquisarReceitas(@RequestParam String titulo) {
        try {
            List<ReceitaDTO> receitas = receitaService.pesquisarReceitasPorTitulo(titulo);
            return ResponseEntity.ok(receitas);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    

    @GetMapping("/tempo-maximo/{tempo}")
    public ResponseEntity<List<ReceitaDTO>> obterReceitasPorTempoMaximo(@PathVariable Integer tempo) {
        try {
            List<ReceitaDTO> receitas = receitaService.obterReceitasPorTempoMaximo(tempo);
            return ResponseEntity.ok(receitas);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    
    @GetMapping("/estatisticas/total")
    public ResponseEntity<Long> contarReceitas() {
        try {
            long total = receitaService.contarReceitas();
            return ResponseEntity.ok(total);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/existe/{id}")
    public ResponseEntity<Boolean> verificarSeReceitaExiste(@PathVariable Long id) {
        try {
            boolean existe = receitaService.existeReceita(id);
            return ResponseEntity.ok(existe);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
