import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../servicos/hibrido_receita_servico.dart';
import '../servicos/hibrido_stock_servico.dart';
import '../servicos/hibrido_utilizador_servico.dart';
import '../models/receita.dart';
import '../models/item_despensa.dart';
import '../widgets/receita_card.dart';
import 'despensa.dart';
import 'frigorifico.dart';
import 'criar_receita.dart';

import 'auth/perfil.dart';
import 'auth/login.dart';
import 'receitas.dart';
import 'receita_detalhes.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  String _userName = '';
  bool _isLoading = false;

  // Serviços simplificados
  final HibridoReceitaService _receitaService = HibridoReceitaService();
  final HibridoStockServico _stockService = HibridoStockServico();
  final HibridoUtilizadorServico _utilizadorService =
      HibridoUtilizadorServico();

  // Dados para estatísticas
  int _totalReceitas = 0;
  int _totalDespensa = 0;
  int _totalFrigorifico = 0;
  List<Receita> _receitasRecentes = [];

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadData();
  }

  List<Widget> get _currentScreens => [
    HomeTab(
      onNavigate: _navigateToTab,
      totalReceitas: _totalReceitas,
      totalDespensa: _totalDespensa,
      totalFrigorifico: _totalFrigorifico,
      receitasRecentes: _receitasRecentes,
      onRefresh: _loadData,
      isLoading: _isLoading,
    ),
    const ReceitasScreen(),
    const DespensaScreen(),
    const FrigorificoScreen(),
  ];

  void _navigateToTab(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  Future<void> _loadUserData() async {
    final utilizador = _utilizadorService.utilizadorAtual;
    setState(() {
      _userName = utilizador?.nome ?? 'Utilizador';
    });
  }

  // Carregar dados dos serviços
  Future<void> _loadData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Carregar receitas
      final receitas = await _receitaService.obterTodasReceitas();

      // Carregar itens do stock
      final itensDespensa = await _stockService.obterItensPorLocalizacao(
        LocalizacaoItem.despensa,
      );
      final itensFrigorifico = await _stockService.obterItensPorLocalizacao(
        LocalizacaoItem.frigorifico,
      );

      setState(() {
        _totalReceitas = receitas.length;
        _totalDespensa = itensDespensa.length;
        _totalFrigorifico = itensFrigorifico.length;
        _receitasRecentes = receitas.take(5).toList();
      });
    } catch (e) {
      debugPrint('Erro ao carregar dados: $e');
      // Mostrar snackbar de erro se necessário
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao carregar dados: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _logout() async {
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Terminar Sessão'),
            content: const Text('Tem certeza que deseja terminar a sessão?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Terminar'),
              ),
            ],
          ),
    );

    if (shouldLogout == true) {
      await _utilizadorService.logout();
      if (mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
          (route) => false,
        );
      }
    }
  }

  void _adicionarNovaReceita() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CriarReceitaScreen()),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (_currentIndex == 0) // Só mostrar no tab Home
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _adicionarNovaReceita,
              tooltip: 'Nova Receita',
            ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'profile') {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const PerfilScreen()),
                );
              } else if (value == 'logout') {
                _logout();
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        const Icon(Icons.person),
                        const SizedBox(width: 8),
                        Text(_userName),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout),
                        SizedBox(width: 8),
                        Text('Terminar Sessão'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: _currentScreens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: AppConstants.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Início'),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Receitas',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory_2),
            label: 'Despensa',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.kitchen),
            label: 'Frigorífico',
          ),
        ],
      ),
      floatingActionButton:
          _currentIndex ==
                  1 // Só mostrar no tab de receitas
              ? FloatingActionButton(
                onPressed: _adicionarNovaReceita,
                backgroundColor: Colors.green,
                child: const Icon(Icons.add, color: Colors.white),
              )
              : null,
    );
  }

  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return 'EST Receitas';
      case 1:
        return 'Receitas';
      case 2:
        return 'Despensa';
      case 3:
        return 'Frigorífico';
      default:
        return 'EST Receitas';
    }
  }
}

class HomeTab extends StatefulWidget {
  final Function(int) onNavigate;
  final int totalReceitas;
  final int totalDespensa;
  final int totalFrigorifico;
  final List<Receita> receitasRecentes;
  final VoidCallback? onRefresh;
  final bool isLoading;

  const HomeTab({
    super.key,
    required this.onNavigate,
    this.totalReceitas = 0,
    this.totalDespensa = 0,
    this.totalFrigorifico = 0,
    this.receitasRecentes = const [],
    this.onRefresh,
    this.isLoading = false,
  });

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header com gradiente
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppConstants.primaryColor,
                  AppConstants.primaryColor.withValues(alpha: 0.8),
                  AppConstants.secondaryColor,
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(30),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.restaurant_menu,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Bem-vindo!',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                'Descubra receitas deliciosas',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Estatísticas rápidas
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            icon: Icons.menu_book,
                            title: '${widget.totalReceitas}',
                            subtitle: 'Receitas',
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            icon: Icons.inventory_2,
                            title: '${widget.totalDespensa}',
                            subtitle: 'Despensa',
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            icon: Icons.kitchen,
                            title: '${widget.totalFrigorifico}',
                            subtitle: 'Frigorifico',
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Acções rápidas
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Acções Rápidas',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 16),

                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    _buildQuickActionCard(
                      context,
                      'Nova Receita',
                      Icons.add_circle,
                      AppConstants.receitasColor,
                      () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const CriarReceitaScreen(),
                          ),
                        );
                      },
                    ),
                    _buildQuickActionCard(
                      context,
                      'Ver Receitas',
                      Icons.menu_book,
                      AppConstants.primaryColor,
                      () => widget.onNavigate(1),
                    ),
                    _buildQuickActionCard(
                      context,
                      'Despensa',
                      Icons.inventory_2,
                      AppConstants.despensaColor,
                      () => widget.onNavigate(2),
                    ),
                    _buildQuickActionCard(
                      context,
                      'Frigorifico',
                      Icons.kitchen,
                      AppConstants.frigorifico,
                      () => widget.onNavigate(3),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Seção de Receitas Recentes
                if (widget.receitasRecentes.isNotEmpty) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Receitas Recentes',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      TextButton(
                        onPressed: () => widget.onNavigate(1),
                        child: const Text(
                          'Ver todas',
                          style: TextStyle(
                            color: AppConstants.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  SizedBox(
                    height: 200,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: widget.receitasRecentes.length,
                      itemBuilder: (context, index) {
                        final receita = widget.receitasRecentes[index];
                        return Container(
                          width: 200,
                          margin: const EdgeInsets.only(right: 16),
                          child: ReceitaCardCompacto(
                            receita: receita,
                            onTap: () async {
                              final resultado = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ReceitaDetalhesScreen(receita: receita),
                                ),
                              );

                              // Se a receita foi eliminada, recarregar os dados
                              if (resultado == true) {
                                widget.onRefresh?.call();
                              }
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
