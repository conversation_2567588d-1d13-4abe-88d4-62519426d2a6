package com.estrecitas.dto;

import com.estrecitas.model.DificuldadeReceita;
import java.time.LocalDateTime;
import java.util.List;

public class ReceitaDTO {
    
    private Long id;
    private String titulo;
    private String descricao;
    private String instrucoes;
    private Integer tempoPreparo;
    private Integer numeroPorcoes;
    private DificuldadeReceita dificuldade;
    private String imagemUrl;
    private List<IngredienteDTO> ingredientes;
    
    // Construtores
    public ReceitaDTO() {}
    
    public ReceitaDTO(String titulo, String descricao, String instrucoes) {
        this.titulo = titulo;
        this.descricao = descricao;
        this.instrucoes = instrucoes;
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitulo() {
        return titulo;
    }
    
    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }
    
    public String getDescricao() {
        return descricao;
    }
    
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public String getInstrucoes() {
        return instrucoes;
    }
    
    public void setInstrucoes(String instrucoes) {
        this.instrucoes = instrucoes;
    }
    
    public Integer getTempoPreparo() {
        return tempoPreparo;
    }
    
    public void setTempoPreparo(Integer tempoPreparo) {
        this.tempoPreparo = tempoPreparo;
    }
    
    public Integer getNumeroPorcoes() {
        return numeroPorcoes;
    }
    
    public void setNumeroPorcoes(Integer numeroPorcoes) {
        this.numeroPorcoes = numeroPorcoes;
    }
    
    public DificuldadeReceita getDificuldade() {
        return dificuldade;
    }
    
    public void setDificuldade(DificuldadeReceita dificuldade) {
        this.dificuldade = dificuldade;
    }
    
    public String getImagemUrl() {
        return imagemUrl;
    }
    
    public void setImagemUrl(String imagemUrl) {
        this.imagemUrl = imagemUrl;
    }
    
    public List<IngredienteDTO> getIngredientes() {
        return ingredientes;
    }
    
    public void setIngredientes(List<IngredienteDTO> ingredientes) {
        this.ingredientes = ingredientes;
    }
    
    @Override
    public String toString() {
        return "ReceitaDTO{" +
                "id=" + id +
                ", titulo='" + titulo + '\'' +
                ", tempoPreparo=" + tempoPreparo +
                ", numeroPorcoes=" + numeroPorcoes +
                '}';
    }
}
