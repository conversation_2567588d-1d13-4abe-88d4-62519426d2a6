<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e6e6b926-ec04-41dc-9e9c-75ea39388c06" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../est_receitas/.vscode/settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/.vscode/settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/android/app/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/android/app/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/android/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/android/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/android/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/android/build.gradle.kts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/android/gradle.properties" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/android/gradle.properties" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/lib/favorites.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/lib/login.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/lib/main.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/lib/main.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/lib/novaReceita.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/lib/registo.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/macos/Flutter/GeneratedPluginRegistrant.swift" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/macos/Flutter/GeneratedPluginRegistrant.swift" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../est_receitas/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../est_receitas/pubspec.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="PhpDebugGeneral" listening_started="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xuVcHV7xLLa6gUGJ21vPycI6HI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.EstReceitasBackendApplication.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.EstReceitasBackendApplication.executor&quot;: &quot;Run&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;https/marianunes&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/EI/2ºSemestre/DesenvolvimentoAplicaçõesMóveis/Trabalho/DAM/est_receitas_backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Application.EstReceitasBackendApplication">
    <configuration name="EstReceitasBackendApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.estrecitas.EstReceitasBackendApplication" />
      <module name="est-receitas-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.estrecitas.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EstReceitasBackendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="est-receitas-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.estrecitas.EstReceitasBackendApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.EstReceitasBackendApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.25659.39" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.25659.39" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e6e6b926-ec04-41dc-9e9c-75ea39388c06" name="Changes" comment="" />
      <created>1748788023083</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748788023083</updated>
      <workItem from="1748788024204" duration="762000" />
      <workItem from="1748807840255" duration="1709000" />
      <workItem from="1748890082580" duration="10076000" />
      <workItem from="1749071314771" duration="11429000" />
      <workItem from="1750356925237" duration="4905000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>