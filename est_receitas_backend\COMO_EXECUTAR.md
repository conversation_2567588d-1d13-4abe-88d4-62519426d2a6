# 🚀 Como Executar o Backend EST Receitas

## ✅ Pré-requisitos

1. **Java 17+** instalado
2. **<PERSON>ven** instalado e configurado no PATH
3. **Porta 8080** disponível

## 🔧 Verificar Instalações

```bash
# Verificar Java
java --version

# Verificar Maven
mvn --version
```

## 🚀 Métodos de Execução

### **Método 1: Script Automático (Recomendado)**

#### Windows:
```cmd
run_backend.bat
```

#### Linux/Mac:
```bash
./run_backend.sh
```

### **Método 2: Maven <PERSON>**

```bash
# Navegar para a pasta do backend
cd est_receitas_backend

# Executar com Maven
mvn spring-boot:run
```

### **Método 3: IDE (IntelliJ IDEA, Eclipse, VS Code)**

1. Abrir projeto `est_receitas_backend`
2. Executar classe `EstReceitasBackendApplication.java`
3. Aguardar inicialização

### **Método 4: Compilar e Executar JAR**

```bash
# Compilar
mvn clean package -DskipTests

# Executar JAR
java -jar target/est-receitas-backend-0.0.1-SNAPSHOT.jar
```

## 🧪 Verificar se Está Funcionando

### **1. Health Check**
```bash
curl http://localhost:8080/api/test/health
```

**Resposta esperada:**
```json
{
  "status": "OK",
  "message": "API está funcionando",
  "timestamp": "2024-06-03T..."
}
```

### **2. No Browser**
- Abrir: `http://localhost:8080/api/test/health`
- Deve mostrar JSON com status "OK"

### **3. Console H2 (Base de Dados)**
- Abrir: `http://localhost:8080/h2-console`
- **JDBC URL:** `jdbc:h2:mem:testdb`
- **Username:** `sa`
- **Password:** `password`

## 📡 Endpoints Disponíveis

### **Teste**
- `GET /api/test/health` - Verificar saúde da API

### **Receitas**
- `GET /api/receitas` - Listar todas as receitas
- `POST /api/receitas` - Criar nova receita
- `GET /api/receitas/{id}` - Obter receita por ID
- `PUT /api/receitas/{id}` - Atualizar receita
- `DELETE /api/receitas/{id}` - Eliminar receita

### **Stock**
- `GET /api/stock` - Listar todos os itens
- `POST /api/stock` - Criar novo item
- `GET /api/stock/{id}` - Obter item por ID
- `PUT /api/stock/{id}` - Atualizar item
- `DELETE /api/stock/{id}` - Eliminar item

### **Autenticação**
- `POST /api/auth/registo` - Registar utilizador
- `POST /api/auth/login` - Fazer login
- `GET /api/auth/profile` - Obter perfil

## 🔧 Resolução de Problemas

### **Erro: "Maven não encontrado"**
```bash
# Verificar se Maven está no PATH
echo $PATH | grep maven

# Adicionar Maven ao PATH (temporário)
export PATH="/caminho/para/maven/bin:$PATH"
```

### **Erro: "Porta 8080 já em uso"**
```bash
# Verificar o que está a usar a porta
netstat -tulpn | grep 8080

# Matar processo na porta 8080
kill -9 $(lsof -t -i:8080)
```

### **Erro: "Java não encontrado"**
```bash
# Verificar versão do Java
java --version

# Definir JAVA_HOME se necessário
export JAVA_HOME=/caminho/para/java
```

## 🎯 Logs Importantes

Quando o backend iniciar com sucesso, deve ver:

```
Started EstReceitasBackendApplication in X.XXX seconds
Tomcat started on port(s): 8080 (http)
```

## 🔗 Conectar com Flutter

1. **Iniciar backend** (porta 8080)
2. **Iniciar Flutter** (`flutter run -d chrome`)
3. **Testar ligação** no menu da app → "Testar Backend"

## 📞 Suporte

Se tiver problemas:

1. Verificar logs do backend
2. Testar endpoints manualmente
3. Verificar configurações de rede/firewall
4. Consultar documentação Spring Boot
