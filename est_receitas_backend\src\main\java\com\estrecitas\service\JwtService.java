package com.estrecitas.service;

import com.estrecitas.model.Utilizador;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * Serviço simples para JWT (sem dependências externas)
 */
@Service
public class JwtService {
    
    private static final String SECRET_KEY = "est-receitas-secret-key-2024-muito-segura";
    private static final String ALGORITHM = "HmacSHA256";
    private static final long EXPIRATION_HOURS = 24; // 24 horas
    
    /**
     * Gerar token JWT para Utilizador
     */
    public String generateToken(Utilizador utilizador) {
        try {
            // Header
            Map<String, Object> header = new HashMap<>();
            header.put("alg", "HS256");
            header.put("typ", "JWT");
            
            // Payload
            long now = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
            long exp = LocalDateTime.now().plusHours(EXPIRATION_HOURS).toEpochSecond(ZoneOffset.UTC);
            
            Map<String, Object> payload = new HashMap<>();
            payload.put("sub", utilizador.getId().toString());
            payload.put("email", utilizador.getEmail());
            payload.put("nome", utilizador.getNome());
            payload.put("iat", now);
            payload.put("exp", exp);
            
            // Criar token
            String headerJson = mapToJson(header);
            String payloadJson = mapToJson(payload);
            
            String headerEncoded = base64UrlEncode(headerJson);
            String payloadEncoded = base64UrlEncode(payloadJson);
            
            String signature = createSignature(headerEncoded + "." + payloadEncoded);
            
            return headerEncoded + "." + payloadEncoded + "." + signature;
            
        } catch (Exception e) {
            throw new RuntimeException("Erro ao gerar token JWT", e);
        }
    }
    
    /**
     * Validar token JWT
     */
    public boolean validateToken(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return false;
            }
            
            String headerEncoded = parts[0];
            String payloadEncoded = parts[1];
            String signature = parts[2];
            
            // Verificar assinatura
            String expectedSignature = createSignature(headerEncoded + "." + payloadEncoded);
            if (!signature.equals(expectedSignature)) {
                return false;
            }
            
            // Verificar expiração
            String payloadJson = base64UrlDecode(payloadEncoded);
            Map<String, Object> payload = jsonToMap(payloadJson);
            
            long exp = ((Number) payload.get("exp")).longValue();
            long now = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC);
            
            return now < exp;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Extrair ID do utilizador do token
     */
    public Long getUserIdFromToken(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return null;
            }
            
            String payloadJson = base64UrlDecode(parts[1]);
            Map<String, Object> payload = jsonToMap(payloadJson);
            
            String sub = (String) payload.get("sub");
            return Long.parseLong(sub);
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Extrair email do utilizador do token
     */
    public String getEmailFromToken(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return null;
            }
            
            String payloadJson = base64UrlDecode(parts[1]);
            Map<String, Object> payload = jsonToMap(payloadJson);
            
            return (String) payload.get("email");
            
        } catch (Exception e) {
            return null;
        }
    }
    

    
    // ===== MÉTODOS AUXILIARES =====
    
    private String createSignature(String data) throws Exception {
        Mac mac = Mac.getInstance(ALGORITHM);
        SecretKeySpec secretKeySpec = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        mac.init(secretKeySpec);
        byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return base64UrlEncode(signature);
    }
    
    private String base64UrlEncode(String data) {
        return base64UrlEncode(data.getBytes(StandardCharsets.UTF_8));
    }
    
    private String base64UrlEncode(byte[] data) {
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }
    
    private String base64UrlDecode(String data) {
        byte[] decoded = Base64.getUrlDecoder().decode(data);
        return new String(decoded, StandardCharsets.UTF_8);
    }
    
    private String mapToJson(Map<String, Object> map) {
        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (!first) json.append(",");
            json.append("\"").append(entry.getKey()).append("\":");
            Object value = entry.getValue();
            if (value instanceof String) {
                json.append("\"").append(value).append("\"");
            } else {
                json.append(value);
            }
            first = false;
        }
        json.append("}");
        return json.toString();
    }
    
    private Map<String, Object> jsonToMap(String json) {
        Map<String, Object> map = new HashMap<>();
        json = json.trim();
        if (json.startsWith("{") && json.endsWith("}")) {
            json = json.substring(1, json.length() - 1);
            String[] pairs = json.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim();
                    if (value.startsWith("\"") && value.endsWith("\"")) {
                        value = value.substring(1, value.length() - 1);
                        map.put(key, value);
                    } else {
                        try {
                            map.put(key, Long.parseLong(value));
                        } catch (NumberFormatException e) {
                            map.put(key, value);
                        }
                    }
                }
            }
        }
        return map;
    }
}
