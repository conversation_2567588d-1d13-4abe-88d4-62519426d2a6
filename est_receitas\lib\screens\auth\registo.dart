import 'package:flutter/material.dart';
import '../../servicos/hibrido_utilizador_servico.dart';
import '../../widgets/common/loading_widget.dart';
import '../../utils/constants.dart';
import '../home.dart';

/// Tela de registo de novos utilizadores
/// Permite que visitantes criem uma nova conta na aplicação
/// Inclui validação de dados e integração com o serviço de autenticação
class RegistoScreen extends StatefulWidget {
  const RegistoScreen({super.key});

  @override
  State<RegistoScreen> createState() => _RegistoScreenState();
}

class _RegistoScreenState extends State<RegistoScreen> {
  // === CONTROLADORES E VALIDAÇÃO ===

  /// Chave global para validação do formulário de registo
  final _formKey = GlobalKey<FormState>();

  /// Controladores para os campos de texto do formulário
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmarPasswordController = TextEditingController();

  /// Serviço responsável pela autenticação e registo de utilizadores
  final _utilizadorService = HibridoUtilizadorServico();

  // === ESTADOS DA INTERFACE ===

  /// Controla a exibição do indicador de carregamento durante o registo
  bool _isLoading = false;

  /// Controla a visibilidade da password no campo principal
  bool _obscurePassword = true;

  /// Controla a visibilidade da password no campo de confirmação
  bool _obscureConfirmarPassword = true;

  /// Limpa os recursos quando o widget é destruído
  @override
  void dispose() {
    // Liberta a memória dos controladores de texto
    _nomeController.dispose();
    _emailController.dispose();
    _telefoneController.dispose();
    _passwordController.dispose();
    _confirmarPasswordController.dispose();
    super.dispose();
  }

  /// Executa o processo de registo de um novo utilizador
  Future<void> _fazerRegisto() async {
    // Valida o formulário antes de prosseguir
    if (!_formKey.currentState!.validate()) return;

    // Ativa o estado de carregamento
    setState(() {
      _isLoading = true;
    });

    try {
      // Chama o serviço de registo com os dados do formulário
      final resultado = await _utilizadorService.registar(
        nome: _nomeController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        telefone:
            _telefoneController.text.trim().isEmpty
                ? null // Telefone é opcional
                : _telefoneController.text.trim(),
      );

      // Verifica se o widget ainda está montado antes de atualizar a UI
      if (mounted) {
        if (resultado.sucesso) {
          // Registo bem-sucedido: mostra mensagem de sucesso
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultado.mensagem),
              backgroundColor: Colors.green,
            ),
          );

          // Navega para a tela principal (substitui a tela atual)
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        } else {
          // Registo falhado: mostra mensagem de erro
          _mostrarErro(resultado.mensagem);
        }
      }
    } catch (e) {
      // Trata erros inesperados
      if (mounted) {
        _mostrarErro('Erro no registo: $e');
      }
    } finally {
      // Desativa o estado de carregamento
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Exibe uma mensagem de erro na parte inferior da tela
  void _mostrarErro(String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red, // Cor vermelha para indicar erro
      ),
    );
  }

  /// Constrói a interface da tela de registo
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50], // Fundo cinza claro
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey, // Associa a chave de validação ao formulário
            child: Column(
              children: [
                // === CABEÇALHO COM LOGO E TÍTULO ===

                // Logo circular com ícone de restaurante
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: const Icon(
                    Icons.restaurant_menu,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),

                // Título da aplicação
                const Text(
                  'EST Receitas',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),

                // Subtítulo explicativo
                const Text(
                  'Preencha os dados para criar a sua conta',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // === TÍTULO DA SEÇÃO DE REGISTO ===
                const Text(
                  'Registar',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // === CAMPOS DO FORMULÁRIO ===

                // Campo de nome completo
                TextFormField(
                  controller: _nomeController,
                  textCapitalization: TextCapitalization.words, // Primeira letra maiúscula
                  decoration: const InputDecoration(
                    labelText: 'Nome Completo',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                  // Validação do nome
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira o nome';
                    }
                    if (value.trim().length < 2) {
                      return 'Nome deve ter pelo menos 2 caracteres';
                    }
                    return null; // Validação passou
                  },
                ),
                const SizedBox(height: 16),

                // Campo de email
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress, // Teclado otimizado para email
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                  // Validação do email
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira o email';
                    }
                    if (!value.contains('@') || !value.contains('.')) {
                      return 'Insira um email válido';
                    }
                    return null; // Validação passou
                  },
                ),
                const SizedBox(height: 16),

                // Campo de telefone (opcional)
                TextFormField(
                  controller: _telefoneController,
                  keyboardType: TextInputType.phone, // Teclado numérico
                  decoration: const InputDecoration(
                    labelText: 'Telefone (opcional)',
                    prefixIcon: Icon(Icons.phone),
                    border: OutlineInputBorder(),
                  ),
                  // Validação do telefone (apenas se preenchido)
                  validator: (value) {
                    if (value != null && value.isNotEmpty && value.length < 9) {
                      return 'O telefone deve ter pelo menos 9 dígitos';
                    }
                    return null; // Campo opcional, pode estar vazio
                  },
                ),
                const SizedBox(height: 16),

                // Campo de password com botão para mostrar/ocultar
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword, // Controla visibilidade da password
                  decoration: InputDecoration(
                    labelText: 'Password',
                    prefixIcon: const Icon(Icons.lock),
                    // Botão para alternar visibilidade da password
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility // Olho fechado
                            : Icons.visibility_off, // Olho aberto
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  // Validação da password
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira a password';
                    }
                    if (value.length < 6) {
                      return 'Password deve ter pelo menos 6 caracteres';
                    }
                    return null; // Validação passou
                  },
                ),
                const SizedBox(height: 16),

                // Campo de confirmação de password
                TextFormField(
                  controller: _confirmarPasswordController,
                  obscureText: _obscureConfirmarPassword, // Controla visibilidade
                  decoration: InputDecoration(
                    labelText: 'Confirmar Password',
                    prefixIcon: const Icon(Icons.lock_outline),
                    // Botão para alternar visibilidade da confirmação
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmarPassword
                            ? Icons.visibility // Olho fechado
                            : Icons.visibility_off, // Olho aberto
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmarPassword =
                              !_obscureConfirmarPassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  // Validação da confirmação de password
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Por favor, confirme a password';
                    }
                    if (value != _passwordController.text) {
                      return 'As passwords não coincidem';
                    }
                    return null; // Passwords coincidem
                  },
                ),
                const SizedBox(height: 32),

                // === BOTÃO DE REGISTO ===
                SizedBox(
                  width: double.infinity, // Ocupa toda a largura
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _fazerRegisto, // Desativa durante loading
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        _isLoading
                            ? const LoadingWidget(size: 20, color: Colors.white) // Loading
                            : const Text(
                              'Registar',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),

                const SizedBox(height: 32),

                // === LINK PARA LOGIN ===
                TextButton(
                  onPressed: () => Navigator.of(context).pop(), // Volta para tela anterior (login)
                  child: const Text(
                    'Já tem conta? Fazer login',
                    style: TextStyle(color: Colors.green),
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
