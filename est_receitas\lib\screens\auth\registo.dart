import 'package:flutter/material.dart';
import '../../servicos/hibrido_utilizador_servico.dart';
import '../../utils/constants.dart';
import '../home.dart';

class RegistoScreen extends StatefulWidget {
  const RegistoScreen({super.key});

  @override
  State<RegistoScreen> createState() => _RegistoScreenState();
}

class _RegistoScreenState extends State<RegistoScreen> {
  final _formKey = GlobalKey<FormState>(); // Chave que identifica de forma única o formulário na árvore de widgets
  final _nomeController = TextEditingController();
  final _emailController = TextEditingController();
  final _telefoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmarPasswordController = TextEditingController();

  // autenticação e registo do utilizador
  final _utilizadorService = HibridoUtilizadorServico();
  
  // Controla a visibilidade da password no campo principal
  bool _obscurePassword = true;
  bool _obscureConfirmarPassword = true;

  // Limpa os recursos quando o widget é destruído
  @override
  void dispose() {
    _nomeController.dispose();
    _emailController.dispose();
    _telefoneController.dispose();
    _passwordController.dispose();
    _confirmarPasswordController.dispose();
    super.dispose();
  }

  // Executa o processo de registo de um novo utilizador
  Future<void> _fazerRegisto() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final resultado = await _utilizadorService.registar(
        nome: _nomeController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        telefone:
            _telefoneController.text.trim().isEmpty
                ? null
                : _telefoneController.text.trim(), // Telefone é opcional
      );

      if (mounted) {
        if (resultado.sucesso) { // Registo com sucesso
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultado.mensagem),
              backgroundColor: Colors.green,
            ),
          );

          // substitui a tela atual e navega para a tela principal
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        } else {
          _mostrarErro(resultado.mensagem);
        }
      }
    } catch (e) {
      if (mounted) {
        _mostrarErro('Erro no registo: $e');
      }
    } 
  }

  void _mostrarErro(String mensagem) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensagem),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50], 
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey, // Associa a chave de validação ao formulário
            child: Column(
              children: [

                // Logotipo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(60),
                  ),
                  child: const Icon(
                    Icons.restaurant_menu,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),

                // Título da app
                const Text(
                  'EST Receitas',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),

                // Subtítulo
                const Text(
                  'Preencha os dados para criar a sua conta',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // titulo de registo
                const Text(
                  'Registar',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                //nome
                TextFormField(
                  controller: _nomeController,
                  decoration: const InputDecoration(
                    labelText: 'Nome',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                  // Validação do nome
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira o nome';
                    }
                    if (value.trim().length < 2) {
                      return 'Nome deve ter pelo menos 2 caracteres';
                    }
                    return null; 
                  },
                ),
                const SizedBox(height: 16),

                // email
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira o email';
                    }
                    if (!value.contains('@') || !value.contains('.')) {
                      return 'Insira um email válido';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // telefone
                TextFormField(
                  controller: _telefoneController,
                  keyboardType: TextInputType.phone, // Teclado numérico
                  decoration: const InputDecoration(
                    labelText: 'Telefone (opcional)',
                    prefixIcon: Icon(Icons.phone),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value != null && value.isNotEmpty && value.length < 9) {
                      return 'O telefone deve ter pelo menos 9 dígitos';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // password com botão para mostrar/ocultar
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword, // Controla visibilidade da password
                  decoration: InputDecoration(
                    labelText: 'Password',
                    prefixIcon: const Icon(Icons.lock),
                    // Botão para alternar visibilidade da password
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword
                            ? Icons.visibility // Olho fechado
                            : Icons.visibility_off, // Olho aberto
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Insira a password';
                    }
                    if (value.length < 6) {
                      return 'Password deve ter pelo menos 6 caracteres';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                TextFormField(
                  controller: _confirmarPasswordController,
                  obscureText: _obscureConfirmarPassword, // Controla visibilidade
                  decoration: InputDecoration(
                    labelText: 'Confirmar Password',
                    prefixIcon: const Icon(Icons.lock_outline),
                    // Botão para alternar visibilidade da confirmação
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmarPassword
                            ? Icons.visibility // Olho fechado
                            : Icons.visibility_off, // Olho aberto
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmarPassword =
                              !_obscureConfirmarPassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  // Validação da confirmação de password
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Por favor, confirme a password';
                    }
                    if (value != _passwordController.text) {
                      return 'As passwords não coincidem';
                    }
                    return null; // Passwords coincidem
                  },
                ),
                const SizedBox(height: 32),

                // botao registo
                SizedBox(
                  width: double.infinity, // Ocupa toda a largura
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _fazerRegisto,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                              'Registar',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),

                const SizedBox(height: 32),

                // link login
                TextButton(
                  onPressed: () => Navigator.of(context).pop(), 
                  child: const Text(
                    'Já tem conta? Fazer login',
                    style: TextStyle(color: Colors.green),
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
