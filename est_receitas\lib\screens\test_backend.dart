import 'package:flutter/material.dart';
import '../servicos/api_servico.dart';
import '../models/receita.dart';
import '../models/item_despensa.dart';
import '../models/ingrediente.dart';

// Ecrã de teste para verificar ligação ao backend
class TestBackendScreen extends StatefulWidget {
  const TestBackendScreen({super.key});

  @override
  State<TestBackendScreen> createState() => _TestBackendScreenState();
}

class _TestBackendScreenState extends State<TestBackendScreen> {
  String _status = 'Aguardar o teste...';
  bool _isLoading = false;
  List<String> _logs = [];

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testarConectividade() async {
    setState(() {
      _isLoading = true;
      _status = 'Testar a conectividade...';
      _logs.clear();
    });

    try {
      _addLog('🔍 Verificar a conectividade com backend...');
      final isConnected = await ApiService.verificarConectividade();
      
      if (isConnected) {
        _addLog('Backend disponível!');
        setState(() {
          _status = 'Backend conectado com sucesso!';
        });
      } else {
        _addLog('Backend não disponível');
        setState(() {
          _status = 'Erro: Backend não responde';
        });
      }
    } catch (e) {
      _addLog('Erro de conectividade: $e');
      setState(() {
        _status = 'Erro: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testarReceitas() async {
    setState(() {
      _isLoading = true;
      _status = 'A restar as receitas...';
    });

    try {
      _addLog('A obter receitas do backend...');
      final receitas = await ApiService.obterTodasReceitas();
      _addLog('Receitas obtidas: ${receitas.length}');

      if (receitas.isNotEmpty) {
        _addLog('Primeira receita: ${receitas.first.titulo}');
      }

      // Testar criação de receita
      _addLog('A criar receita de teste...');
      final novaReceita = Receita(
        titulo: 'Receita de Teste ${DateTime.now().millisecondsSinceEpoch}',
        descricao: 'Receita criada para testar a ligação ao backend',
        ingredientes: [
          const Ingrediente(nome: 'Ingrediente Teste', quantidade: 1.0, unidade: 'unidade'),
        ],
        instrucoes: 'Instruções de teste para verificar se a receita é guardada no backend.',
        tempoPreparo: 10,
        numeroPorcoes: 1,
        dificuldade: 'Fácil',
      );

      final receitaCriada = await ApiService.criarReceita(novaReceita);
      _addLog('Receita criada com ID: ${receitaCriada.id}');

      setState(() {
        _status = 'Teste de receitas concluído com sucesso!';
      });
    } catch (e) {
      _addLog('Erro no teste de receitas: $e');
      setState(() {
        _status = 'Erro no teste de receitas: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testarStock() async {
    setState(() {
      _isLoading = true;
      _status = 'A testar stock...';
    });

    try {
      _addLog('Obtendo itens do stock...');
      final itens = await ApiService.obterTodosItens();
      _addLog('✅ Itens obtidos: ${itens.length}');

      if (itens.isNotEmpty) {
        _addLog('Primeiro item: ${itens.first.nome}');
      }

      // Testar criação de item
      _addLog('Criando item de teste...');
      final novoItem = ItemDespensa(
        nome: 'Item Teste ${DateTime.now().millisecondsSinceEpoch}',
        quantidade: 1.0,
        unidade: 'unidade',
        localizacao: LocalizacaoItem.despensa,
      );

      final itemCriado = await ApiService.adicionarItem(novoItem);
      _addLog('Item criado com ID: ${itemCriado.id}');

      setState(() {
        _status = 'Teste de stock concluído com sucesso!';
      });
    } catch (e) {
      _addLog('Erro no teste de stock: $e');
      setState(() {
        _status = 'Erro no teste de stock: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _executarTodosTestes() async {
    await _testarConectividade();
    if (_status.contains('sucesso')) {
      await _testarReceitas();
      await _testarStock();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Teste Backend'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status
            Card(
              color: _status.contains('sucesso') 
                  ? Colors.green[50] 
                  : _status.contains('Erro') 
                      ? Colors.red[50] 
                      : Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _status.contains('sucesso') 
                          ? Icons.check_circle 
                          : _status.contains('Erro') 
                              ? Icons.error 
                              : Icons.info,
                      size: 48,
                      color: _status.contains('sucesso') 
                          ? Colors.green 
                          : _status.contains('Erro') 
                              ? Colors.red 
                              : Colors.blue,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Botões de teste
            if (!_isLoading) ...[
              ElevatedButton.icon(
                onPressed: _testarConectividade,
                icon: const Icon(Icons.wifi),
                label: const Text('Testar Conectividade'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _testarReceitas,
                icon: const Icon(Icons.restaurant),
                label: const Text('Testar Receitas'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _testarStock,
                icon: const Icon(Icons.inventory),
                label: const Text('Testar Stock'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _executarTodosTestes,
                icon: const Icon(Icons.play_arrow),
                label: const Text('Executar Todos os Testes'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ] else ...[
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],

            const SizedBox(height: 16),

            // Logs
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'Logs:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        itemCount: _logs.length,
                        itemBuilder: (context, index) {
                          final log = _logs[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 4.0),
                            child: Text(
                              log,
                              style: TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                                color: log.contains('❌') 
                                    ? Colors.red 
                                    : log.contains('✅') 
                                        ? Colors.green 
                                        : Colors.black87,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
