# 💻 **EXEMPLOS DE CÓDIGO COMENTADO PARA O GRUPO**

## 🎯 **CÓDIGO REAL EXPLICADO LINHA A LINHA**

Este ficheiro contém exemplos reais do código do projeto com comentários detalhados para explicar aos colegas.

---

## 📱 **EXEMPLO 1: MAIN.DART - INÍCIO DA APLICAÇÃO**

```dart
// Importações - "ingredientes" que precisamos
import 'package:flutter/material.dart';
import 'screens/auth/login_screen.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';
import 'services/storage_service.dart';
import 'services/connectivity_service.dart';
import 'services/hybrid_utilizador_service.dart';

// FUNÇÃO PRINCIPAL - Por onde tudo começa (como o main() em Java/C)
void main() async {
  // Preparar Flutter para funcionar
  WidgetsFlutterBinding.ensureInitialized();
  
  // Preparar armazenamento local (como abrir uma base de dados)
  await StorageService.init();

  // Inicializar serviços importantes
  ConnectivityService().initialize();           // Serviço de internet
  await HybridUtilizadorService().inicializar(); // Serviço de utilizadores

  // EXECUTAR A APLICAÇÃO
  runApp(const MyApp());
}

// CLASSE PRINCIPAL DA APLICAÇÃO
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'EST Receitas',                    // Nome da aplicação
      debugShowCheckedModeBanner: false,       // Remover banner de debug
      
      // TEMA VISUAL da aplicação
      theme: ThemeData(
        useMaterial3: true,                     // Usar Material Design 3
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppConstants.primaryColor, // Cor principal (verde)
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,        // Texto branco na barra
        ),
      ),
      
      // ECRÃ INICIAL - SplashScreen (carregamento)
      home: const SplashScreen(),
      
      // ROTAS - Como navegar entre ecrãs
      routes: {
        '/login': (context) => const LoginScreen(),
        '/home': (context) => const HomeScreen(),
      },
    );
  }
}

// ECRÃ DE CARREGAMENTO (aparece 2 segundos)
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final _utilizadorService = HybridUtilizadorService();

  @override
  void initState() {
    super.initState();
    _checkLoginStatus(); // Verificar se utilizador já está logado
  }

  Future<void> _checkLoginStatus() async {
    // Simular carregamento (mostrar logo por 2 segundos)
    await Future.delayed(const Duration(seconds: 2));

    // Verificar se utilizador já fez login antes
    if (mounted) { // Verificar se widget ainda existe
      if (_utilizadorService.isAutenticado) {
        // JÁ ESTÁ LOGADO - ir para ecrã principal
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        // NÃO ESTÁ LOGADO - ir para ecrã de login
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.primaryColor, // Fundo verde
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // LOGO da aplicação
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.restaurant_menu,
                size: 60,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            
            // TÍTULO
            const Text(
              'EST Receitas',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            
            // SUBTÍTULO
            const Text(
              'Gerir receitas e despensa',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            
            const SizedBox(height: 40),
            
            // INDICADOR DE CARREGAMENTO
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
```

---

## 🔧 **EXEMPLO 2: SERVIÇO HÍBRIDO - LÓGICA PRINCIPAL**

```dart
// SERVIÇO HÍBRIDO DE RECEITAS - A inovação principal do projeto
class HybridReceitaService {
  // TRÊS "FUNCIONÁRIOS" que trabalham em equipa
  final ServicoReceitas _localService = ServicoReceitas();     // Funcionário LOCAL
  final ApiReceitaService _apiService = ApiReceitaService();   // Funcionário da API
  final ConnectivityService _connectivityService = ConnectivityService(); // Verificador de internet

  // OBTER TODAS AS RECEITAS - Método principal
  Future<List<Receita>> obterTodasReceitas() async {
    try {
      // PASSO 1: SEMPRE obter dados locais primeiro (RÁPIDO)
      final receitasLocais = await _localService.obterReceitas();

      // PASSO 2: Se há internet, tentar obter dados da API
      if (_connectivityService.isOnline) {
        try {
          // Obter receitas do servidor
          final receitasApi = await _apiService.obterTodasReceitas();

          // PASSO 3: COMBINAR dados (API tem prioridade)
          final receitasMescladas = _mergeReceitas(receitasLocais, receitasApi);

          // PASSO 4: Atualizar cache local com dados mais recentes
          await _atualizarCacheLocal(receitasMescladas);

          // RETORNAR dados combinados (melhor qualidade)
          return receitasMescladas;
        } catch (e) {
          // Se API falhar, usar dados locais (FALLBACK)
          debugPrint('Erro ao sincronizar com API: $e');
        }
      }

      // PASSO 5: Retornar dados locais (sempre funciona)
      return receitasLocais;
    } catch (e) {
      throw Exception('Erro ao obter receitas: $e');
    }
  }

  // CRIAR NOVA RECEITA - Estratégia offline-first
  Future<Receita> criarReceita(Receita receita) async {
    try {
      // PASSO 1: SEMPRE salvar localmente primeiro (GARANTIA)
      final receitaLocal = await _localService.guardarReceita(receita);

      // PASSO 2: Se há internet, tentar criar na API
      if (_connectivityService.isOnline) {
        try {
          // Criar receita no servidor
          final receitaApi = await _apiService.criarReceita(receita);

          // PASSO 3: Atualizar receita local com ID do servidor
          final receitaAtualizada = receitaLocal.copyWith(id: receitaApi.id?.toString());
          await _localService.guardarReceita(receitaAtualizada);

          // RETORNAR receita com ID do servidor
          return receitaAtualizada;
        } catch (e) {
          debugPrint('Erro ao criar receita na API: $e');
          // PASSO 4: Marcar para sincronização posterior
          await _marcarParaSincronizacao(receitaLocal, 'CREATE');
        }
      } else {
        // SEM INTERNET: Marcar para sincronização quando voltar online
        await _marcarParaSincronizacao(receitaLocal, 'CREATE');
      }

      // SEMPRE retorna receita (mesmo sem internet)
      return receitaLocal;
    } catch (e) {
      throw Exception('Erro ao criar receita: $e');
    }
  }

  // COMBINAR receitas locais e da API
  List<Receita> _mergeReceitas(List<Receita> locais, List<Receita> api) {
    final Map<String, Receita> receitasMap = {};

    // PASSO 1: Adicionar receitas locais
    for (final receita in locais) {
      if (receita.id != null) {
        receitasMap[receita.id!] = receita;
      }
    }

    // PASSO 2: Sobrescrever com receitas da API (têm prioridade)
    for (final receita in api) {
      if (receita.id != null) {
        receitasMap[receita.id!] = receita;
      }
    }

    // RETORNAR lista combinada
    return receitasMap.values.toList();
  }

  // MARCAR receita para sincronização posterior
  Future<void> _marcarParaSincronizacao(Receita receita, String operacao) async {
    // TODO: Implementar fila de sincronização
    debugPrint('Receita marcada para sincronização: ${receita.titulo} - $operacao');
  }
}
```

---

## 📱 **EXEMPLO 3: ECRÃ COM ESTADO - HOME SCREEN**

```dart
// ECRÃ PRINCIPAL - Tem estado que pode mudar
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // VARIÁVEIS DE ESTADO - Controlam o que aparece no ecrã
  int _currentIndex = 0;          // Que tab está ativo (0=Home, 1=Receitas, etc.)
  String _userName = '';          // Nome do utilizador
  bool _isLoading = false;        // Se está a carregar dados
  bool _isSyncing = false;        // Se está a sincronizar

  // SERVIÇOS que este ecrã usa
  final HybridReceitaService _receitaService = HybridReceitaService();
  final HybridStockService _stockService = HybridStockService();
  final HybridUtilizadorService _utilizadorService = HybridUtilizadorService();

  // DADOS para mostrar estatísticas
  int _totalReceitas = 0;
  int _totalDespensa = 0;
  int _totalFrigorifico = 0;
  List<Receita> _receitasRecentes = [];

  @override
  void initState() {
    super.initState();
    _loadUserData();  // Carregar dados do utilizador
    _loadData();      // Carregar dados principais
  }

  // CARREGAR dados do utilizador
  Future<void> _loadUserData() async {
    final utilizador = _utilizadorService.utilizadorAtual;
    setState(() {
      _userName = utilizador?.nome ?? 'Utilizador';
    });
  }

  // CARREGAR dados principais (receitas, stock)
  Future<void> _loadData() async {
    if (_isLoading) return; // Evitar carregar múltiplas vezes

    setState(() {
      _isLoading = true; // MOSTRAR indicador de carregamento
    });

    try {
      // OBTER dados dos serviços híbridos
      final receitas = await _receitaService.obterTodasReceitas();
      final itensDespensa = await _stockService.obterItensPorLocalizacao(LocalizacaoItem.despensa);
      final itensFrigorifico = await _stockService.obterItensPorLocalizacao(LocalizacaoItem.frigorifico);

      setState(() {
        // ATUALIZAR estatísticas
        _totalReceitas = receitas.length;
        _totalDespensa = itensDespensa.length;
        _totalFrigorifico = itensFrigorifico.length;
        _receitasRecentes = receitas.take(5).toList(); // Últimas 5 receitas
      });
    } catch (e) {
      debugPrint('Erro ao carregar dados: $e');
      
      // MOSTRAR erro ao utilizador
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao carregar dados: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false; // ESCONDER indicador de carregamento
        });
      }
    }
  }

  // SINCRONIZAR dados com o servidor
  Future<void> _syncData() async {
    if (_isSyncing || !_connectivityService.isOnline) return;

    setState(() {
      _isSyncing = true; // MOSTRAR que está a sincronizar
    });

    try {
      // SINCRONIZAR receitas e stock
      await _receitaService.sincronizar();
      await _stockService.sincronizar();

      // RECARREGAR dados após sincronização
      await _loadData();

      if (mounted) {
        // MOSTRAR sucesso
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Dados sincronizados com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Erro ao sincronizar dados: $e');
      if (mounted) {
        // MOSTRAR erro
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao sincronizar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false; // ESCONDER indicador de sincronização
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // BARRA SUPERIOR
      appBar: AppBar(
        title: Text(_getAppBarTitle()), // Título muda conforme o tab
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // ÍCONE de conectividade (🟢🔴🟡)
          const ConnectivityIcon(size: 20),
          const SizedBox(width: 8),
          
          // BOTÃO de sincronização
          SyncButton(
            onSync: _syncData,
            isLoading: _isSyncing,
          ),
          
          // MENU do utilizador
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'logout') {
                _logout();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    const Icon(Icons.logout),
                    const SizedBox(width: 8),
                    Text('Terminar Sessão'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      
      // CONTEÚDO principal (muda conforme o tab)
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator()) // LOADING
        : _currentScreens[_currentIndex],                  // CONTEÚDO
      
      // NAVEGAÇÃO inferior (4 tabs)
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index; // MUDAR tab ativo
          });
        },
        selectedItemColor: AppConstants.primaryColor,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.favorite), label: 'Receitas'),
          BottomNavigationBarItem(icon: Icon(Icons.inventory_2), label: 'Despensa'),
          BottomNavigationBarItem(icon: Icon(Icons.kitchen), label: 'Frigorífico'),
        ],
      ),
    );
  }

  // OBTER título da barra conforme o tab ativo
  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0: return 'EST Receitas';
      case 1: return 'Receitas Favoritas';
      case 2: return 'Despensa';
      case 3: return 'Frigorífico';
      default: return 'EST Receitas';
    }
  }
}
```

---

## 🎯 **PONTOS-CHAVE DESTES EXEMPLOS**

### **1. Estrutura Clara**
- **Importações** no topo
- **Variáveis de estado** bem organizadas
- **Métodos** com nomes descritivos
- **Comentários** explicativos

### **2. Gestão de Estado**
- **setState()** para atualizar interface
- **Variáveis booleanas** para controlar loading
- **Verificação mounted** antes de atualizar

### **3. Tratamento de Erros**
- **try/catch** em todas as operações
- **SnackBar** para mostrar erros ao utilizador
- **Fallback** para dados locais

### **4. Estratégia Híbrida**
- **Local primeiro** (sempre funciona)
- **API depois** (melhora quando possível)
- **Merge inteligente** (combina os dois)
- **Sincronização** automática

**🎯 Estes exemplos mostram como o código está bem estruturado, comentado e segue boas práticas de desenvolvimento Flutter!** 🇵🇹✨
