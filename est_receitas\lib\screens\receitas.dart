import 'package:flutter/material.dart';
import '../models/receita.dart';
import '../servicos/hibrido_receita_servico.dart';
import '../widgets/receita_card.dart';
import 'receita_detalhes.dart';

class ReceitasScreen extends StatefulWidget {
  const ReceitasScreen({super.key});

  @override
  State<ReceitasScreen> createState() => _ReceitasScreenState();
}

class _ReceitasScreenState extends State<ReceitasScreen> {
  final HibridoReceitaService _receitaService = HibridoReceitaService();
  List<Receita> _receitas = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _carregarReceitas();
  }

  Future<void> _carregarReceitas() async {
    setState(() => _isLoading = true);
    try {
      final receitas = await _receitaService.obterTodasReceitas();
      setState(() {
        _receitas = receitas;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erro ao carregar receitas: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(

      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _receitas.isEmpty
              ? const Center(child: Text('Nenhuma receita encontrada.'))
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _receitas.length,
                  itemBuilder: (context, index) {
                    final receita = _receitas[index];
                    return ReceitaCard(
                      receita: receita,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ReceitaDetalhesScreen(receita: receita),
                          ),
                        );
                      },
                    );
                  },
                ),
    );
  }
}
