package com.estrecitas.dto;

import com.estrecitas.model.Utilizador;
import java.time.LocalDateTime;

/**
 * Representa o perfil de um utilizador sem expor informações sensíveis como palavras-passe.
 * Esta classe é utilizada para transferir dados de perfil entre a camada de serviço e a camada de apresentação.
 */
public class PerfilUtilizador {
    
    private Long id;
    private String nome;
    private String email;
    
    /**
     * Construtor padrão
     */
    public PerfilUtilizador() {}
    
    /**
     * Construtor para criar um perfil de utilizador a partir de uma entidade Utilizador.
     * 
     * @param utilizador A entidade Utilizador de onde serão copiados os dados
     */
    public PerfilUtilizador(Utilizador utilizador) {
        this.id = utilizador.getId();
        this.nome = utilizador.getNome();
        this.email = utilizador.getEmail();
    }
    
    /**
     * Construtor completo com todos os campos
     */
    public PerfilUtilizador(Long id, String nome, String email) {
        this.id = id;
        this.nome = nome;
        this.email = email;
    }
    
    // Método estático para criar a partir de Utilizador
    public static PerfilUtilizador fromUtilizador(Utilizador utilizador) {
        return new PerfilUtilizador(utilizador);
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }

    
    @Override
    public String toString() {
        return "PerfilUtilizador{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
