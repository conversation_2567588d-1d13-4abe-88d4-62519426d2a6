package com.estrecitas.dto;

import com.estrecitas.model.Utilizador;
import java.time.LocalDateTime;

/**
 * Representa o perfil de um utilizador sem expor informações sensíveis como palavras-passe.
 * Esta classe é utilizada para transferir dados de perfil entre a camada de serviço e a camada de apresentação.
 */
public class PerfilUtilizador {
    
    private Long id;
    private String nome;
    private String email;
    private String role;
    private LocalDateTime dataCriacao;
    private LocalDateTime dataUltimoLogin;
    private Boolean ativo;
    
    /**
     * Construtor padrão
     */
    public PerfilUtilizador() {}
    
    /**
     * Construtor para criar um perfil de utilizador a partir de uma entidade Utilizador.
     * 
     * @param utilizador A entidade Utilizador de onde serão copiados os dados
     */
    public PerfilUtilizador(Utilizador utilizador) {
        this.id = utilizador.getId();
        this.nome = utilizador.getNome();
        this.email = utilizador.getEmail();
        this.dataCriacao = utilizador.getDataCriacao();
        this.dataUltimoLogin = utilizador.getDataUltimoLogin();
        this.ativo = utilizador.getAtivo();
    }
    
    /**
     * Construtor completo com todos os campos
     */
    public PerfilUtilizador(Long id, String nome, String email, String role, 
                    LocalDateTime dataCriacao, LocalDateTime dataUltimoLogin, Boolean ativo) {
        this.id = id;
        this.nome = nome;
        this.email = email;
        this.role = role;
        this.dataCriacao = dataCriacao;
        this.dataUltimoLogin = dataUltimoLogin;
        this.ativo = ativo;
    }
    
    // Método estático para criar a partir de Utilizador
    public static PerfilUtilizador fromUtilizador(Utilizador utilizador) {
        return new PerfilUtilizador(utilizador);
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public LocalDateTime getDataCriacao() {
        return dataCriacao;
    }
    
    public void setDataCriacao(LocalDateTime dataCriacao) {
        this.dataCriacao = dataCriacao;
    }
    
    public LocalDateTime getDataUltimoLogin() {
        return dataUltimoLogin;
    }
    
    public void setDataUltimoLogin(LocalDateTime dataUltimoLogin) {
        this.dataUltimoLogin = dataUltimoLogin;
    }
    
    public Boolean getAtivo() {
        return ativo;
    }
    
    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }
    
    @Override
    public String toString() {
        return "PerfilUtilizador{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                ", role='" + role + '\'' +
                ", dataCriacao=" + dataCriacao +
                ", dataUltimoLogin=" + dataUltimoLogin +
                ", ativo=" + ativo +
                '}';
    }
}
