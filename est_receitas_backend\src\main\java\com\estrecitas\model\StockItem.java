package com.estrecitas.model;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Entity
@Table(name = "stock_items")
public class StockItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String nome;
    
    @Column(nullable = false)
    private Double quantidade;
    
    @Column(nullable = false, length = 50)
    private String unidade;
    
    @Column(name = "data_validade")
    private LocalDate dataValidade;
    
    @Column(name = "localizacao", nullable = false)
    @Enumerated(EnumType.STRING)
    private TipoArmazenamento localizacao;
    
    @Column(name = "data_adicao")
    private LocalDateTime dataAdicao;
    
    @Column(name = "data_atualizacao")
    private LocalDateTime dataAtualizacao;
    
    @Column(length = 500)
    private String observacoes;
    
    // Construtores
    public StockItem() {
        this.dataAdicao = LocalDateTime.now();
        this.dataAtualizacao = LocalDateTime.now();
    }
    
    public StockItem(String nome, Double quantidade, String unidade, TipoArmazenamento localizacao) {
        this();
        this.nome = nome;
        this.quantidade = quantidade;
        this.unidade = unidade;
        this.localizacao = localizacao;
    }
    
    public StockItem(String nome, Double quantidade, String unidade, TipoArmazenamento localizacao, LocalDate dataValidade) {
        this(nome, quantidade, unidade, localizacao);
        this.dataValidade = dataValidade;
    }
    
    // Métodos de callback JPA
    @PreUpdate
    public void preUpdate() {
        this.dataAtualizacao = LocalDateTime.now();
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Double getQuantidade() {
        return quantidade;
    }
    
    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
    
    public String getUnidade() {
        return unidade;
    }
    
    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }
    
    public LocalDate getDataValidade() {
        return dataValidade;
    }
    
    public void setDataValidade(LocalDate dataValidade) {
        this.dataValidade = dataValidade;
    }
    
    public TipoArmazenamento getLocalizacao() {
        return localizacao;
    }

    public void setLocalizacao(TipoArmazenamento localizacao) {
        this.localizacao = localizacao;
    }
    
    public LocalDateTime getDataAdicao() {
        return dataAdicao;
    }
    
    public void setDataAdicao(LocalDateTime dataAdicao) {
        this.dataAdicao = dataAdicao;
    }
    
    public LocalDateTime getDataAtualizacao() {
        return dataAtualizacao;
    }
    
    public void setDataAtualizacao(LocalDateTime dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }
    
    public String getObservacoes() {
        return observacoes;
    }
    
    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }
    
    // Métodos auxiliares
    public boolean isVencido() {
        return dataValidade != null && dataValidade.isBefore(LocalDate.now());
    }
    
    public boolean isProximoDoVencimento(int diasLimite) {
        if (dataValidade == null) return false;
        
        LocalDate hoje = LocalDate.now();
        long diasParaVencer = ChronoUnit.DAYS.between(hoje, dataValidade);
        
        return diasParaVencer >= 0 && diasParaVencer <= diasLimite;
    }
    
    public long getDiasParaVencer() {
        if (dataValidade == null) return Long.MAX_VALUE;
        
        return ChronoUnit.DAYS.between(LocalDate.now(), dataValidade);
    }
    
    public String getQuantidadeFormatada() {
        if (quantidade == null) return "";
        
        // Se for um número inteiro, não mostrar casas decimais
        if (quantidade % 1 == 0) {
            return String.valueOf(quantidade.intValue());
        }
        
        return String.valueOf(quantidade);
    }
    
    public String getDescricaoCompleta() {
        StringBuilder sb = new StringBuilder();
        sb.append(nome);
        
        if (quantidade != null && unidade != null) {
            sb.append(" - ").append(getQuantidadeFormatada()).append(" ").append(unidade);
        }

        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "StockItem{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", quantidade=" + quantidade +
                ", unidade='" + unidade + '\'' +
                ", localizacao=" + localizacao +
                ", dataValidade=" + dataValidade +
                '}';
    }
}
