package com.estrecitas.dto;

import com.estrecitas.model.TipoArmazenamento;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class StockItemDTO {
    
    private Long id;
    private String nome;
    private Double quantidade;
    private String unidade;
    private LocalDate dataValidade;
    private TipoArmazenamento localizacao;
    private LocalDateTime dataAdicao;
    private LocalDateTime dataAtualizacao;
    private String observacoes;
    
    // Construtores
    public StockItemDTO() {}
    
    public StockItemDTO(String nome, Double quantidade, String unidade, TipoArmazenamento localizacao) {
        this.nome = nome;
        this.quantidade = quantidade;
        this.unidade = unidade;
        this.localizacao = localizacao;
    }
    
    public StockItemDTO(String nome, Double quantidade, String unidade, TipoArmazenamento localizacao, LocalDate dataValidade) {
        this(nome, quantidade, unidade, localizacao);
        this.dataValidade = dataValidade;
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Double getQuantidade() {
        return quantidade;
    }
    
    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
    
    public String getUnidade() {
        return unidade;
    }
    
    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }
    
    public LocalDate getDataValidade() {
        return dataValidade;
    }
    
    public void setDataValidade(LocalDate dataValidade) {
        this.dataValidade = dataValidade;
    }
    
    public TipoArmazenamento getLocalizacao() {
        return localizacao;
    }

    public void setLocalizacao(TipoArmazenamento localizacao) {
        this.localizacao = localizacao;
    }
    
    public LocalDateTime getDataAdicao() {
        return dataAdicao;
    }
    
    public void setDataAdicao(LocalDateTime dataAdicao) {
        this.dataAdicao = dataAdicao;
    }
    
    public LocalDateTime getDataAtualizacao() {
        return dataAtualizacao;
    }
    
    public void setDataAtualizacao(LocalDateTime dataAtualizacao) {
        this.dataAtualizacao = dataAtualizacao;
    }
    
    public String getObservacoes() {
        return observacoes;
    }
    
    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }
    
    // Métodos auxiliares
    public String getQuantidadeFormatada() {
        if (quantidade == null) return "";
        
        // Se for um número inteiro, não mostrar casas decimais
        if (quantidade % 1 == 0) {
            return String.valueOf(quantidade.intValue());
        }
        
        return String.valueOf(quantidade);
    }
    
    /**
     * Devolve a descrição completa do item de stock.
     * Formata o nome do item juntamente com a quantidade e unidade (se disponíveis).
     * 
     * @return Uma string com o nome do item e, opcionalmente, a quantidade e unidade
     */
    public String getDescricaoCompleta() {
        StringBuilder sb = new StringBuilder();
        sb.append(nome);
        
        if (quantidade != null && unidade != null) {
            sb.append(" - ").append(getQuantidadeFormatada()).append(" ").append(unidade);
        }

        return sb.toString();
    }
    
    /**
     * Sobrescreve o método toString() para representar o objeto em formato texto.
     * 
     * @return Uma string com os principais atributos do objeto
     */
    @Override
    public String toString() {
        return "StockItemDTO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", quantidade=" + quantidade +
                ", unidade='" + unidade + '\'' +
                ", localizacao=" + localizacao +
                ", dataValidade=" + dataValidade +
                '}';
    }
}
