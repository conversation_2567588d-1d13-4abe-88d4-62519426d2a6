import 'package:flutter/material.dart';
import 'screens/auth/login.dart';
import 'screens/home.dart';
import 'utils/constants.dart';
import 'servicos/hibrido_utilizador_servico.dart';
import 'servicos/servico_armazenamento.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar armazenamento simples para utilizadores
  await ServicoArmazenamento.init();

  // Inicializar serviço de autenticação
  await HibridoUtilizadorServico().inicializar();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'EST Receitas',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppConstants.primaryColor,
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
      ),
      home: const SplashScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/home': (context) => const HomeScreen(),
      },
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final _utilizadorServico = HibridoUtilizadorServico();

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  Future<void> _checkLoginStatus() async {
    // Pausa de 2 segundos para simular carregamento inicial e permitir que a splash screen seja visualizada
    await Future.delayed(const Duration(seconds: 2));

    // Verificar se o utilizador já está autenticado no sistema
    if (mounted) {
      // Se o widget ainda estiver na árvore de widgets (para evitar erros de setState)
      if (_utilizadorServico.isAutenticado) {
        // Se o utilizador estiver autenticado, redireciona para a página principal
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        // Caso não esteja autenticado, redireciona para a página de login
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Define a cor de fundo como a cor primária da aplicação
      backgroundColor: AppConstants.primaryColor,
      body: Center(
        child: Column(
          // Centraliza os elementos verticalmente na tela
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo da aplicação - Container branco com ícone de menu
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                // Fundo branco
                color: Colors.white,
                // Bordas arredondadas
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                // Ícone representativo de receitas/alimentação
                Icons.restaurant_menu,
                size: 60,
                // Utiliza a cor primária para o ícone
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'EST Receitas',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Gerir receitas e despensa',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 40),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}


