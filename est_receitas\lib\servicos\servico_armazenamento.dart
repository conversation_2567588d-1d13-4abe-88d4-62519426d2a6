import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Serviço de armazenamento simples apenas para utilizadores
/// (Não é uma base de dados própria, apenas cache local de autenticação)
class ServicoArmazenamento {
  static SharedPreferences? _prefs;

  /// Inicializar o serviço
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Obter instância do SharedPreferences
  static SharedPreferences get instance {
    if (_prefs == null) {
      throw Exception('ServicoArmazenamento não foi inicializado. Chame init() primeiro.');
    }
    return _prefs!;
  }

  /// Guardar string
  static Future<bool> setString(String key, String value) async {
    await init();
    return await instance.setString(key, value);
  }

  /// Obter string
  static Future<String?> getString(String key) async {
    await init();
    return instance.getString(key);
  }

  /// Guardar objeto
  static Future<bool> saveObject<T>(
    String key,
    T object,
    Map<String, dynamic> Function(T) toMap,
  ) async {
    await init();
    final json = jsonEncode(toMap(object));
    return await instance.setString(key, json);
  }

  /// Carregar objeto
  static Future<T?> loadObject<T>(
    String key,
    T Function(Map<String, dynamic>) fromMap,
  ) async {
    await init();
    final json = instance.getString(key);
    if (json == null) return null;
    
    try {
      final map = jsonDecode(json) as Map<String, dynamic>;
      return fromMap(map);
    } catch (e) {
      return null;
    }
  }

  /// Guardar lista de objetos
  static Future<bool> saveObjectList<T>(
    String key,
    List<T> objects,
    Map<String, dynamic> Function(T) toMap,
  ) async {
    await init();
    final jsonList = objects.map((obj) => toMap(obj)).toList();
    final json = jsonEncode(jsonList);
    return await instance.setString(key, json);
  }

  /// Carregar lista de objetos
  static Future<List<T>> loadObjectList<T>(
    String key,
    T Function(Map<String, dynamic>) fromMap,
  ) async {
    await init();
    final json = instance.getString(key);
    if (json == null) return [];
    
    try {
      final jsonList = jsonDecode(json) as List<dynamic>;
      return jsonList
          .cast<Map<String, dynamic>>()
          .map((map) => fromMap(map))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Eliminar chave
  static Future<bool> delete(String key) async {
    await init();
    return await instance.remove(key);
  }

  /// Verificar se chave existe
  static Future<bool> containsKey(String key) async {
    await init();
    return instance.containsKey(key);
  }

  /// Limpar tudo
  static Future<bool> clear() async {
    await init();
    return await instance.clear();
  }
}
