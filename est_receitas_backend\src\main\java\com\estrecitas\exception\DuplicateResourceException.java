package com.estrecitas.exception;

/**
 * Exceção quando se tenta criar um recurso que já existe no sistema.
 * Esta exceção é utilizada para tratar casos de duplicação de recursos como 
 * utilizadores com o mesmo email, ingredientes com o mesmo nome, etc.
 */
public class DuplicateResourceException extends RuntimeException {
    
    public DuplicateResourceException(String message) {
        super(message);
    }
    
    public DuplicateResourceException(String resourceName, String fieldName, Object fieldValue) {
        super(String.format("%s já existe com %s: '%s'", resourceName, fieldName, fieldValue));
    }
    
    public DuplicateResourceException(String message, Throwable cause) {
        super(message, cause);
    }
}
