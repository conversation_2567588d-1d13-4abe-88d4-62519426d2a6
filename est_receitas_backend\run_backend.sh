#!/bin/bash

echo "========================================"
echo "  EST Receitas Backend - Iniciando..."
echo "========================================"

# Verificar se Maven está disponível
if ! command -v mvn &> /dev/null; then
    echo "[ERRO] Maven não encontrado no PATH"
    echo ""
    echo "Soluções possíveis:"
    echo "1. Instalar Maven: https://maven.apache.org/download.cgi"
    echo "2. Adicionar Maven ao PATH: export PATH=/caminho/para/maven/bin:\$PATH"
    echo "3. Usar IDE (IntelliJ IDEA, Eclipse, VS Code)"
    echo ""
    exit 1
fi

echo "[INFO] Maven encontrado!"
mvn --version

echo ""
echo "[INFO] Compilando e iniciando o backend..."
echo "[INFO] URL: http://localhost:8080"
echo "[INFO] Health Check: http://localhost:8080/api/test/health"
echo ""
echo "Pressione Ctrl+C para parar o servidor"
echo "========================================"

# Executar o backend
mvn spring-boot:run
