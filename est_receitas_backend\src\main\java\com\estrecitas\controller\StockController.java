package com.estrecitas.controller;

import com.estrecitas.dto.StockItemDTO;
import com.estrecitas.model.TipoArmazenamento;
import com.estrecitas.service.StockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/stock")
@CrossOrigin(origins = "*")
public class StockController {
    
    @Autowired
    private StockService stockService;
    
    // ===== OPERAÇÕES BÁSICAS =====
    
    @GetMapping
    public ResponseEntity<List<StockItemDTO>> obterTodosItens() {
        try {
            List<StockItemDTO> itens = stockService.obterTodosItens();
            return ResponseEntity.ok(itens);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<StockItemDTO> obterItemPorId(@PathVariable Long id) {
        try {
            Optional<StockItemDTO> item = stockService.obterItemPorId(id);
            return item.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PostMapping
    public ResponseEntity<StockItemDTO> adicionarItem(@RequestBody StockItemDTO stockItemDTO) {
        try {
            StockItemDTO novoItem = stockService.adicionarItem(stockItemDTO);
            return ResponseEntity.status(HttpStatus.CREATED).body(novoItem);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<StockItemDTO> atualizarItem(@PathVariable Long id, @RequestBody StockItemDTO stockItemDTO) {
        try {
            StockItemDTO itemAtualizado = stockService.atualizarItem(id, stockItemDTO);
            if (itemAtualizado != null) {
                return ResponseEntity.ok(itemAtualizado);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PatchMapping("/{id}/quantidade")
    public ResponseEntity<StockItemDTO> atualizarQuantidade(@PathVariable Long id, @RequestParam Double quantidade) {
        try {
            StockItemDTO itemAtualizado = stockService.atualizarQuantidade(id, quantidade);
            if (itemAtualizado != null) {
                return ResponseEntity.ok(itemAtualizado);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @PatchMapping("/{id}/validade")
    public ResponseEntity<StockItemDTO> atualizarValidade(@PathVariable Long id, @RequestParam LocalDate dataValidade) {
        try {
            StockItemDTO itemAtualizado = stockService.atualizarValidade(id, dataValidade);
            if (itemAtualizado != null) {
                return ResponseEntity.ok(itemAtualizado);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> removerItem(@PathVariable Long id) {
        try {
            boolean removido = stockService.removerItem(id);
            if (removido) {
                return ResponseEntity.noContent().build();
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/despensa")
    public ResponseEntity<List<StockItemDTO>> obterItensDespensa() {
        try {
            List<StockItemDTO> itens = stockService.obterItensDespensa();
            return ResponseEntity.ok(itens);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    @GetMapping("/frigorifico")
    public ResponseEntity<List<StockItemDTO>> obterItensFrigorifico() {
        try {
            List<StockItemDTO> itens = stockService.obterItensFrigorifico();
            return ResponseEntity.ok(itens);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    
    @GetMapping("/pesquisar")
    public ResponseEntity<List<StockItemDTO>> pesquisarItens(@RequestParam String nome) {
        try {
            List<StockItemDTO> itens = stockService.pesquisarItensPorNome(nome);
            return ResponseEntity.ok(itens);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    
    @GetMapping("/unidades")
    public ResponseEntity<List<String>> obterTodasUnidades() {
        try {
            List<String> unidades = stockService.obterTodasUnidades();
            return ResponseEntity.ok(unidades);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    @GetMapping("/estatisticas/total")
    public ResponseEntity<Long> contarItens() {
        try {
            long total = stockService.contarItens();
            return ResponseEntity.ok(total);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
