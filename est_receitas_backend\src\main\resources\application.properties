# Configuração do Servidor
server.port=8080

# Configuração da Base de Dados H2 (para desenvolvimento)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Configuração JPA/Hibernate
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Configuração de inicialização de dados - COMPLETAMENTE DESABILITADA
spring.jpa.defer-datasource-initialization=false
spring.sql.init.mode=never
spring.datasource.initialization-mode=never

# Configuração CORS para Flutter
spring.web.cors.allowed-origins=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Configuração de Segurança (temporariamente desabilitada)
spring.security.user.name=admin
spring.security.user.password=admin
spring.security.user.roles=ADMIN

# Configuração de Logging
logging.level.com.estrecitas=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# Configuração de DevTools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
