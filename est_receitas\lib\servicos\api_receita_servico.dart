import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/receita.dart';
import '../models/ingrediente.dart';
import '../config/api_config.dart';

// Serviço de API para comunicação com o backend Spring Boot - Receitas
class ApiReceitaService {

  // ===== OPERAÇÕES BÁSICAS =====

  // Obter todas as receitas
  Future<List<Receita>> obterTodasReceitas() async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET ${ApiConfig.receitasUrl}');
      }

      final response = await http.get(
        Uri.parse(ApiConfig.receitasUrl),
        headers: ApiConfig.defaultHeaders,
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final receitas = data.map((json) => _receitaFromBackendJson(json)).toList();

        if (ApiConfig.enableLogging) {
          debugPrint('Receitas obtidas: ${receitas.length}');
        }

        return receitas;
      } else {
        throw Exception('Erro ao obter receitas: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao obter receitas: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // Obter receita por ID
  Future<Receita?> obterReceitaPorId(int id) async {
    try {
      final url = ApiConfig.receitaPorId(id);

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _receitaFromBackendJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Erro ao obter receita: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao obter receita por ID: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // Criar nova receita
  Future<Receita> criarReceita(Receita receita) async {
    try {
      final body = json.encode(_receitaToBackendJson(receita));

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST ${ApiConfig.receitasUrl}');
        if (ApiConfig.logRequestBody) {
          debugPrint('📤 Body: $body');
        }
      }      debugPrint('🔍 URI completo: ${Uri.parse(ApiConfig.receitasUrl)}');
      debugPrint('🔍 Headers: ${ApiConfig.jsonHeaders}');
      debugPrint('🔍 Body completo: $body');
      
      final response = await http.post(
        Uri.parse(ApiConfig.receitasUrl),
        headers: ApiConfig.jsonHeaders,
        body: body,
      );

      debugPrint('🔍 Status code: ${response.statusCode}');
      debugPrint('🔍 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _receitaFromBackendJson(data);
      } else {
        throw Exception('Erro ao criar receita: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao criar receita: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // Atualizar receita
  Future<Receita> atualizarReceita(Receita receita) async {
    if (receita.id == null) {
      throw Exception('O ID da receita é obrigatório para a atualização');
    }

    try {
      final idInt = int.parse(receita.id!);
      final url = ApiConfig.receitaPorId(idInt);
      final body = json.encode(_receitaToBackendJson(receita));

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 PUT $url');
      }

      final response = await http.put(
        Uri.parse(url),
        headers: ApiConfig.jsonHeaders,
        body: body,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _receitaFromBackendJson(data);
      } else {
        throw Exception('Erro ao atualizar receita: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao atualizar receita: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // Eliminar receita
  Future<bool> eliminarReceita(int id) async {
    try {
      final url = ApiConfig.receitaPorId(id);

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 DELETE $url');
      }

      final response = await http.delete(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      );

      return response.statusCode == 204;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao eliminar receita: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // Pesquisar receitas por título
  Future<List<Receita>> pesquisarReceitasPorTitulo(String titulo) async {
    try {
      final url = ApiConfig.buildUrl(ApiConfig.receitasPesquisarUrl, {'titulo': titulo});

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _receitaFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao pesquisar receitas: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('Erro ao pesquisar receitas: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== MÉTODOS PRIVADOS DE CONVERSÃO =====
  // Converte receita do backend para modelo Flutter
  Receita _receitaFromBackendJson(Map<String, dynamic> json) {
    return Receita(
      id: json['id']?.toString(),
      titulo: json['titulo'] ?? '',
      descricao: json['descricao'] ?? '',
      instrucoes: json['instrucoes'] ?? '',
      tempoPreparo: json['tempoPreparo'],
      numeroPorcoes: json['numeroPorcoes'],
      dificuldade: json['dificuldade']?.toString(),
      imagemUrl: json['imagemUrl'],
      ingredientes: (json['ingredientes'] as List<dynamic>?)
          ?.map((ing) => Ingrediente(
                nome: ing['nome'] ?? '',
                quantidade: ing['quantidade']?.toDouble() ?? 0.0,
                unidade: ing['unidade'] ?? '',
              ))
          .toList() ?? [],
    );
  }  // Converte receita do Flutter para formato do backend
  Map<String, dynamic> _receitaToBackendJson(Receita receita) {
    // Converter dificuldade para formato esperado pelo backend (enum)
    String dificuldadeEnum = 'MEDIO'; // Valor padrão
    if (receita.dificuldade != null) {
      if (receita.dificuldade!.toLowerCase().contains('fácil') ||
          receita.dificuldade!.toLowerCase().contains('facil')) {
        dificuldadeEnum = 'FACIL';
      } else if (receita.dificuldade!.toLowerCase().contains('difícil') ||
                receita.dificuldade!.toLowerCase().contains('dificil')) {
        dificuldadeEnum = 'DIFICIL';
      }
    }

    // Mapear ingredientes para formato esperado pelo backend
    final ingredientes = receita.ingredientes.map((ing) => {
      'nome': ing.nome,
      'quantidade': ing.quantidade,
      'unidade': ing.unidade,
    }).toList();

    // Criar objeto JSON no formato exato esperado pelo backend
    final receitaJson = {
      if (receita.id != null && receita.id!.isNotEmpty) 'id': int.tryParse(receita.id!),
      'titulo': receita.titulo,
      'descricao': receita.descricao,
      'instrucoes': receita.instrucoes,
      'tempoPreparo': receita.tempoPreparo ?? 0,
      'numeroPorcoes': receita.numeroPorcoes ?? 1,
      'dificuldade': dificuldadeEnum,
      'imagemUrl': receita.imagemUrl,
      'ingredientes': ingredientes,
    };

    debugPrint('🔄 Convertendo receita para backend: $receitaJson');
    return receitaJson;
  }
}
