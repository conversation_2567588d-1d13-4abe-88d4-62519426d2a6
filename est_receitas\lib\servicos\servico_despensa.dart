import 'package:flutter/foundation.dart';
import '../models/item_despensa.dart';
import 'servico_armazenamento.dart';

/// Serviço local de despensa (armazenamento local apenas)
class ServicoDespensa {
  static const String _keyItens = 'itens_despensa_locais';

  // ===== OPERAÇÕES BÁSICAS =====

  /// Obter todos os itens
  Future<List<ItemDespensa>> obterTodosItens() async {
    try {
      final itens = await ServicoArmazenamento.loadObjectList<ItemDespensa>(
        _keyItens,
        (map) => ItemDespensa.fromMap(map),
      );
      
      // Se não há itens, criar alguns de exemplo
      if (itens.isEmpty) {
        return await _criarItensExemplo();
      }
      
      return itens;
    } catch (e) {
      debugPrint('Erro ao obter itens: $e');
      return await _criarItensExemplo();
    }
  }

  /// Obter item por ID
  Future<ItemDespensa?> obterItemPorId(int id) async {
    try {
      final itens = await obterTodosItens();
      return itens.firstWhere(
        (item) => item.id == id,
        orElse: () => throw Exception('Item não encontrado'),
      );
    } catch (e) {
      debugPrint('Item não encontrado: $id');
      return null;
    }
  }

  /// Obter itens por localização
  Future<List<ItemDespensa>> obterItensPorLocalizacao(LocalizacaoItem localizacao) async {
    try {
      final itens = await obterTodosItens();
      return itens.where((item) => item.localizacao == localizacao).toList();
    } catch (e) {
      debugPrint('Erro ao obter itens por localização: $e');
      return [];
    }
  }

  /// Adicionar item
  Future<ItemDespensa> adicionarItem(ItemDespensa item) async {
    try {
      final itens = await obterTodosItens();
      
      // Gerar ID se não existe
      if (item.id == null) {
        final maxId = itens.isEmpty ? 0 : itens.map((i) => i.id ?? 0).reduce((a, b) => a > b ? a : b);
        item = item.copyWith(id: maxId + 1);
      }
      
      itens.add(item);
      await _guardarItens(itens);
      return item;
    } catch (e) {
      throw Exception('Erro ao adicionar item: $e');
    }
  }

  /// Atualizar item
  Future<ItemDespensa> atualizarItem(ItemDespensa item) async {
    try {
      final itens = await obterTodosItens();
      final index = itens.indexWhere((i) => i.id == item.id);
      
      if (index >= 0) {
        itens[index] = item;
        await _guardarItens(itens);
        return item;
      } else {
        throw Exception('Item não encontrado para atualização');
      }
    } catch (e) {
      throw Exception('Erro ao atualizar item: $e');
    }
  }

  /// Eliminar item
  Future<bool> eliminarItem(int id) async {
    try {
      final itens = await obterTodosItens();
      final index = itens.indexWhere((i) => i.id == id);
      
      if (index >= 0) {
        itens.removeAt(index);
        await _guardarItens(itens);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Erro ao eliminar item: $e');
      return false;
    }
  }

  /// Pesquisar itens por nome
  Future<List<ItemDespensa>> pesquisarItensPorNome(String nome) async {
    try {
      final itens = await obterTodosItens();
      final nomeLower = nome.toLowerCase();
      
      return itens.where((item) => 
        item.nome.toLowerCase().contains(nomeLower)).toList();
    } catch (e) {
      debugPrint('Erro ao pesquisar itens: $e');
      return [];
    }
  }

  /// Obter itens próximos do vencimento
  Future<List<ItemDespensa>> obterItensProximosVencimento({int dias = 7}) async {
    try {
      final itens = await obterTodosItens();
      final agora = DateTime.now();
      final limite = agora.add(Duration(days: dias));
      
      return itens.toList();
    } catch (e) {
      debugPrint('Erro ao obter itens próximos do vencimento: $e');
      return [];
    }
  }

  /// Obter itens vencidos
  Future<List<ItemDespensa>> obterItensVencidos() async {
    try {
      final itens = await obterTodosItens();
      final agora = DateTime.now();
      
      return itens.toList();
    } catch (e) {
      debugPrint('Erro ao obter itens vencidos: $e');
      return [];
    }
  }

  /// Obter itens com estoque baixo
  Future<List<ItemDespensa>> obterItensEstoqueBaixo({double limite = 5.0}) async {
    try {
      final itens = await obterTodosItens();
      return itens.where((item) => item.quantidade <= limite).toList();
    } catch (e) {
      debugPrint('Erro ao obter itens com estoque baixo: $e');
      return [];
    }
  }

  // ===== MÉTODOS PRIVADOS =====

  Future<void> _guardarItens(List<ItemDespensa> itens) async {
    await ServicoArmazenamento.saveObjectList<ItemDespensa>(
      _keyItens,
      itens,
      (i) => i.toMap(),
    );
  }

  Future<List<ItemDespensa>> _criarItensExemplo() async {
    final agora = DateTime.now();
    final itens = [
      ItemDespensa(
        id: 1,
        nome: 'Arroz',
        quantidade: 2.0,
        unidade: 'kg',
        localizacao: LocalizacaoItem.despensa,
        categoria: 'Cereais',
      ),
      ItemDespensa(
        id: 2,
        nome: 'Leite',
        quantidade: 1.0,
        unidade: 'L',
        localizacao: LocalizacaoItem.frigorifico,
        categoria: 'Lacticínios',
      ),
      ItemDespensa(
        id: 3,
        nome: 'Ovos',
        quantidade: 12.0,
        unidade: 'unidades',
        localizacao: LocalizacaoItem.frigorifico,
        categoria: 'Proteínas',
      ),
      ItemDespensa(
        id: 4,
        nome: 'Azeite',
        quantidade: 0.5,
        unidade: 'L',
        localizacao: LocalizacaoItem.despensa,
        categoria: 'Óleos',
      ),
      ItemDespensa(
        id: 5,
        nome: 'Tomate',
        quantidade: 3.0,
        unidade: 'unidades',
        localizacao: LocalizacaoItem.frigorifico,
        categoria: 'Vegetais',
      ),
    ];

    await _guardarItens(itens);
    return itens;
  }
}
