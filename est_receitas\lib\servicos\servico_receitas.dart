import 'package:flutter/foundation.dart';
import '../models/receita.dart';
import '../models/ingrediente.dart';
import 'servico_armazenamento.dart';

// Serviço local de receitas (armazenamento local apenas)
class ServicoReceitas {
  static const String _keyReceitas = 'receitas_locais';

  // ===== OPERAÇÕES BÁSICAS =====

  // Obter todas as receitas
  Future<List<Receita>> obterReceitas() async {
    try {
      final receitas = await ServicoArmazenamento.loadObjectList<Receita>(
        _keyReceitas,
        (map) => Receita.fromMap(map),
      );
      
      // Se não há receitas, criar algumas de exemplo
      if (receitas.isEmpty) {
        return await _criarReceitasExemplo();
      }
      
      return receitas;
    } catch (e) {
      debugPrint('Erro ao obter receitas: $e');
      return await _criarReceitasExemplo();
    }
  }

  // Obter receita por ID
  Future<Receita?> obterReceitaPorId(String id) async {
    try {
      final receitas = await obterReceitas();
      return receitas.firstWhere(
        (r) => r.id == id,
        orElse: () => throw Exception('Receita não encontrada'),
      );
    } catch (e) {
      debugPrint('Receita não encontrada: $id');
      return null;
    }
  }

  // Guardar receita
  Future<Receita> guardarReceita(Receita receita) async {
    try {
      final receitas = await obterReceitas();
      
      // Se é uma receita nova, gerar ID
      if (receita.id == null || receita.id!.isEmpty) {
        receita = receita.copyWith(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
        );
      }
      
      // Verificar se já existe
      final index = receitas.indexWhere((r) => r.id == receita.id);
      if (index >= 0) {
        receitas[index] = receita;
      } else {
        receitas.add(receita);
      }
      
      await _guardarReceitas(receitas);
      return receita;
    } catch (e) {
      throw Exception('Erro ao guardar receita: $e');
    }
  }

  // Eliminar receita
  Future<bool> eliminarReceita(String id) async {
    try {
      final receitas = await obterReceitas();
      final index = receitas.indexWhere((r) => r.id == id);
      
      if (index >= 0) {
        receitas.removeAt(index);
        await _guardarReceitas(receitas);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Erro ao eliminar receita: $e');
      return false;
    }
  }

  // Pesquisar receitas por título
  Future<List<Receita>> pesquisarReceitas(String termo) async {
    try {
      final receitas = await obterReceitas();
      final termoLower = termo.toLowerCase();
      
      return receitas.where((receita) {
        return receita.titulo.toLowerCase().contains(termoLower) ||
               receita.descricao.toLowerCase().contains(termoLower) ||
               receita.ingredientes.any((ing) => 
                 ing.nome.toLowerCase().contains(termoLower));
      }).toList();
    } catch (e) {
      debugPrint('Erro ao pesquisar receitas: $e');
      return [];
    }
  }



  // ===== MÉTODOS PRIVADOS =====

  Future<void> _guardarReceitas(List<Receita> receitas) async {
    await ServicoArmazenamento.saveObjectList<Receita>(
      _keyReceitas,
      receitas,
      (r) => r.toMap(),
    );
  }

  Future<List<Receita>> _criarReceitasExemplo() async {
    final receitas = [
      Receita(
        id: '1',
        titulo: 'Arroz de Frango',
        descricao: 'Delicioso arroz com frango e legumes',
        ingredientes: [
          Ingrediente(nome: 'Arroz', quantidade: 2, unidade: 'chávenas'),
          Ingrediente(nome: 'Frango', quantidade: 500, unidade: 'g'),
          Ingrediente(nome: 'Cebola', quantidade: 1, unidade: 'unidade'),
          Ingrediente(nome: 'Alho', quantidade: 2, unidade: 'dentes'),
        ],
        instrucoes: 'Refogar o frango, adicionar o arroz e temperos, cozinhar com água.',
        tempoPreparo: 45,
        dificuldade: 'MEDIO',
      ),
      Receita(
        id: '2',
        titulo: 'Salada Mista',
        descricao: 'Salada fresca com legumes variados',
        ingredientes: [
          Ingrediente(nome: 'Alface', quantidade: 1, unidade: 'unidade'),
          Ingrediente(nome: 'Tomate', quantidade: 2, unidade: 'unidades'),
          Ingrediente(nome: 'Pepino', quantidade: 1, unidade: 'unidade'),
          Ingrediente(nome: 'Azeite', quantidade: 2, unidade: 'colheres'),
        ],
        instrucoes: 'Lavar e cortar os legumes, temperar com azeite e sal.',
        tempoPreparo: 15,
        dificuldade: 'FACIL',
      ),
      Receita(
        id: '3',
        titulo: 'Bolo de Chocolate',
        descricao: 'Bolo húmido de chocolate',
        ingredientes: [
          Ingrediente(nome: 'Farinha', quantidade: 2, unidade: 'chávenas'),
          Ingrediente(nome: 'Chocolate', quantidade: 200, unidade: 'g'),
          Ingrediente(nome: 'Ovos', quantidade: 3, unidade: 'unidades'),
          Ingrediente(nome: 'Açúcar', quantidade: 1, unidade: 'chávena'),
        ],
        instrucoes: 'Misturar ingredientes, levar ao forno por 30 minutos.',
        tempoPreparo: 60,
        dificuldade: 'MEDIO',
      ),
    ];

    await _guardarReceitas(receitas);
    return receitas;
  }
}
