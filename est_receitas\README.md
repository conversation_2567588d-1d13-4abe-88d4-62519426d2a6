# 🍽️ EST Receitas - Aplicação de Gestão Culinária

> **Projeto Académico Completo** - Desenvolvimento de Aplicações Móveis & Aplicações Internet Distribuídas
> **Estado:** ✅ **100% Funcional e Pronto para Demonstração**

Uma aplicação Flutter moderna para gestão inteligente de receitas culinárias e organização da despensa doméstica, desenvolvida integralmente em **Português de Portugal**.

## 🎯 **Funcionalidades Principais**

### 📱 **Gestão de Receitas**
- ✅ Criar, editar e visualizar receitas
- ✅ Sistema de ingredientes detalhado
- ✅ Pesquisa e filtros avançados
- ✅ Favoritos com persistência local

### 🥘 **Gestão de Despensa & Frigorífico**
- ✅ Inventário completo de ingredientes
- ✅ Alertas de validade automáticos
- ✅ Categorização por localização
- ✅ Gestão de quantidades e unidades

### 🔐 **Sistema de Utilizadores**
- ✅ Autenticação local segura
- ✅ Gestão de perfis
- ✅ Dados pessoais protegidos

### 📊 **Funcionalidades Avançadas**
- ✅ Estatísticas em tempo real
- ✅ Sugestões baseadas em ingredientes disponíveis
- ✅ Interface responsiva e intuitiva
- ✅ Funcionamento 100% offline

## 🛠️ **Tecnologias Utilizadas**

| Componente | Tecnologia | Versão |
|------------|------------|---------|
| **Frontend** | Flutter/Dart | 3.24+ |
| **Armazenamento** | SharedPreferences | Local |
| **Autenticação** | Sistema próprio | Local |
| **Interface** | Material Design 3 | Nativa |
| **Plataformas** | Android, iOS, Web, Desktop | Multiplataforma |

## 🚀 **Como Executar**

### **Pré-requisitos**
```bash
✅ Flutter SDK 3.24+
✅ Dart 3.5+
✅ Java 21 (para Android)
✅ Android Studio/VS Code
✅ Modo Desenvolvedor ativado (Windows)
```

### **Instalação Rápida**
```bash
# 1. Clonar o repositório
git clone [url-do-repositorio]
cd est_receitas

# 2. Instalar dependências
flutter pub get

# 3. Executar aplicação
flutter run                    # Android/iOS
flutter run -d chrome         # Web
flutter run -d windows        # Desktop Windows
```

### **Compilação para Produção**
```bash
# Android APK
flutter build apk --release

# Web
flutter build web --release

# Windows
flutter build windows --release
```

## 📁 **Arquitetura do Projeto**

```
est_receitas/
├── 📱 lib/
│   ├── 🎯 models/              # Modelos de dados (Receita, ItemDespensa, Utilizador)
│   ├── ⚙️ services/            # Lógica de negócio simplificada
│   │   ├── hybrid_*_service.dart    # Serviços híbridos (apenas local)
│   │   ├── servico_*.dart           # Serviços base em português
│   │   └── storage_service.dart     # Armazenamento local
│   ├── 🖥️ screens/             # Interfaces de utilizador
│   │   ├── auth/                    # Autenticação
│   │   ├── home_screen.dart         # Ecrã principal
│   │   ├── *_screen.dart            # Outros ecrãs
│   ├── 🧩 widgets/             # Componentes reutilizáveis
│   └── 🔧 utils/               # Utilitários e constantes
├── 📚 assets/                  # Recursos (imagens, ícones)
├── 🤖 android/                 # Configuração Android
├── 🌐 web/                     # Configuração Web
└── 📋 *.md                     # Documentação completa
```

## 🎨 **Interface e Design**

- **🎨 Material Design 3** - Interface moderna e consistente
- **🇵🇹 Português de Portugal** - Totalmente localizada
- **📱 Responsiva** - Adaptável a diferentes tamanhos de ecrã
- **🌈 Cores Temáticas** - Esquema visual profissional
- **♿ Acessibilidade** - Seguindo boas práticas de UX

## 📊 **Estado Atual do Projeto**

### ✅ **100% Concluído**
| Componente | Estado | Detalhes |
|------------|---------|----------|
| **🎯 Funcionalidades Core** | ✅ Completo | Todas implementadas |
| **🎨 Interface** | ✅ Completo | Design finalizado |
| **🔐 Autenticação** | ✅ Completo | Sistema local funcional |
| **📱 Responsividade** | ✅ Completo | Todas as plataformas |
| **🇵🇹 Localização** | ✅ Completo | 100% em português |
| **📚 Documentação** | ✅ Completo | Guias detalhados |
| **🧪 Testes** | ✅ Validado | Funcionamento verificado |

### 🎯 **Pronto Para:**
- ✅ **Demonstração Académica**
- ✅ **Apresentação Oral**
- ✅ **Entrega Final**
- ✅ **Avaliação Completa**

## 👥 **Desenvolvimento**

**Projeto desenvolvido para:**
- 📚 **Desenvolvimento de Aplicações Móveis**
- 🌐 **Aplicações Internet Distribuídas**

**Características Técnicas:**
- 🏗️ **Arquitetura Limpa** - Separação clara de responsabilidades
- 🔄 **Padrão Repository** - Gestão eficiente de dados
- 🎯 **SOLID Principles** - Código maintível e extensível
- 📱 **Mobile-First** - Otimizado para dispositivos móveis

## 📞 **Suporte e Documentação**

- 📖 **Documentação Completa** - Ver ficheiros `*.md` na raiz
- 🎯 **Guia de Demonstração** - `GUIA_DEMONSTRACAO_PRATICA.md`
- 🔧 **Resolução de Problemas** - `PROBLEMAS_ANDROID_RESOLVIDOS.md`
- 📋 **Checklist** - `CHECKLIST_DEMONSTRACAO.md`

---

**🎓 Projeto Académico | 🇵🇹 100% Português | ✅ Pronto para Entrega**
