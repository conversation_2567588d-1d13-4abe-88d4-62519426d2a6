# EST Receitas

Aplicação Flutter para gestão de receitas culinárias e controlo de despensa.

## Funcionalidades

- Sistema de autenticação (login/registo)
- Gestão de receitas (criar, editar, visualizar, eliminar)
- Controlo de despensa e frigorífico
- Funcionamento offline com armazenamento local
- Interface responsiva

## Tecnologias

- Flutter/Dart
- SharedPreferences para armazenamento local
- HTTP para comunicação com API
- Material Design

## Como Executar

1. Instalar Flutter SDK
2. Clonar o repositório
3. Executar `flutter pub get`
4. Executar `flutter run`

## Estrutura

```
lib/
├── models/          # Modelos de dados (Utilizador, Receita, etc.)
├── screens/         # Telas da aplicação
├── servicos/        # Serviços de dados e lógica
├── widgets/         # Componentes reutilizáveis
└── utils/           # Constantes e utilitários
```

## Funcionalidades Principais

### Autenticação
- Login e registo de utilizadores
- Gestão de perfil
- Sessão persistente

### Receitas
- Lista e pesquisa de receitas
- Criar e editar receitas
- Gestão de ingredientes
- Diferentes níveis de dificuldade

### Despensa
- Controlo de stock de ingredientes
- Organização por localização
- Gestão de quantidades

## Arquitetura

A aplicação usa uma arquitetura híbrida:
- Dados armazenados localmente (SharedPreferences)
- Sincronização com backend quando disponível
- Funcionamento offline garantido

## Estado Atual

- ✅ Autenticação funcional
- ✅ CRUD de receitas
- ✅ Interface completa
- ✅ Armazenamento local
- 🚧 Integração com backend
