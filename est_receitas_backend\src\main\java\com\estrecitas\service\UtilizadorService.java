package com.estrecitas.service;

import com.estrecitas.dto.RegistoRequest;
import com.estrecitas.dto.PerfilUtilizador;
import com.estrecitas.model.Utilizador;
import com.estrecitas.repository.UtilizadorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service para gestão de utilizadores
 */
@Service
@Transactional
public class UtilizadorService {
    
    @Autowired
    private UtilizadorRepository utilizadorRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // ===== OPERAÇÕES BÁSICAS =====
    
    /**
     * Registar novo utilizador
     */
    public Utilizador registarUtilizador(RegistoRequest request) {
        // Verificar se email já existe
        if (utilizadorRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + request.getEmail());
        }
        
        // Verificar se passwords coincidem
        if (!request.isPasswordMatch()) {
            throw new RuntimeException("Passwords não coincidem");
        }
        
        // Criar novo utilizador
        Utilizador utilizador = new Utilizador();
        utilizador.setNome(request.getNome());
        utilizador.setEmail(request.getEmail().toLowerCase());
        utilizador.setPassword(passwordEncoder.encode(request.getPassword()));
        utilizador.setAtivo(true);
        
        return utilizadorRepository.save(utilizador);
    }
    
    /**
     * Autenticar utilizador
     */
    public Optional<Utilizador> authenticateUtilizador(String email, String password) {
        Optional<Utilizador> utilizadorOpt = utilizadorRepository.findByEmail(email.toLowerCase());
        
        if (utilizadorOpt.isPresent()) {
            Utilizador utilizador = utilizadorOpt.get();
            
            // Verificar se utilizador está ativo
            if (!utilizador.getAtivo()) {
                throw new RuntimeException("Conta desativada");
            }
            
            // Verificar password
            if (passwordEncoder.matches(password, utilizador.getPassword())) {
                // Atualizar último login
                utilizador.atualizarUltimoLogin();
                utilizadorRepository.save(utilizador);
                return Optional.of(utilizador);
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * Obter utilizador por ID
     */
    public Optional<Utilizador> getUtilizadorById(Long id) {
        return utilizadorRepository.findById(id);
    }
    
    /**
     * Obter utilizador por email
     */
    public Optional<Utilizador> getUtilizadorByEmail(String email) {
        return utilizadorRepository.findByEmail(email.toLowerCase());
    }
    
    /**
     * Obter perfil de utilizador
     */
    public Optional<PerfilUtilizador> getUtilizadorPerfil(Long id) {
        return utilizadorRepository.findById(id)
                .map(PerfilUtilizador::fromUtilizador);
    }
    
    /**
     * Atualizar perfil de utilizador
     */
    public Utilizador atualizarUtilizadorPerfil(Long id, String nome, String email) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        // Verificar se novo email já está em uso (por outro utilizador)
        if (!utilizador.getEmail().equals(email.toLowerCase())) {
            if (utilizadorRepository.existsByEmail(email.toLowerCase())) {
                throw new RuntimeException("Email já está em uso: " + email);
            }
        }
        
        utilizador.setNome(nome);
        utilizador.setEmail(email.toLowerCase());
        
        return utilizadorRepository.save(utilizador);
    }
    
    /**
     * Alterar password
     */
    public void changePassword(Long id, String currentPassword, String newPassword) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        // Verificar password atual
        if (!passwordEncoder.matches(currentPassword, utilizador.getPassword())) {
            throw new RuntimeException("Password atual incorreta");
        }
        
        // Atualizar password
        utilizador.setPassword(passwordEncoder.encode(newPassword));
        utilizadorRepository.save(utilizador);
    }
    
    /**
     * Desativar utilizador
     */
    public void deactivateUtilizador(Long id) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        utilizador.setAtivo(false);
        utilizadorRepository.save(utilizador);
    }
    
    /**
     * Ativar utilizador
     */
    public void activateUtilizador(Long id) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        utilizador.setAtivo(true);
        utilizadorRepository.save(utilizador);
    }
    
    // ===== OPERAÇÕES DE CONSULTA =====
    
    /**
     * Listar todos os utilizadores ativos
     */
    public List<PerfilUtilizador> getAllActiveUtilizadores() {
        return utilizadorRepository.findByAtivoTrue()
                .stream()
                .map(PerfilUtilizador::fromUtilizador)
                .collect(Collectors.toList());
    }
    
    /**
     * Pesquisar utilizadores por nome
     */
    public List<PerfilUtilizador> searchUtilizadoresByName(String nome) {
        return utilizadorRepository.findByNomeContainingIgnoreCaseAndAtivoTrue(nome)
                .stream()
                .map(PerfilUtilizador::fromUtilizador)
                .collect(Collectors.toList());
    }
    
    /**
     * Contar utilizadores ativos
     */
    public long countActiveUtilizadores() {
        return utilizadorRepository.countByAtivoTrue();
    }
    
    /**
     * Verificar se email existe
     */
    public boolean emailExists(String email) {
        return utilizadorRepository.existsByEmail(email.toLowerCase());
    }
    
    /**
     * Obter utilizadores inativos
     */
    public List<PerfilUtilizador> getInactiveUtilizadores(int days) {
        LocalDateTime dataLimite = LocalDateTime.now().minusDays(days);
        return utilizadorRepository.findInactiveUsers(dataLimite)
                .stream()
                .map(PerfilUtilizador::fromUtilizador)
                .collect(Collectors.toList());
    }
}
