package com.estrecitas.service;

import com.estrecitas.dto.RegistoRequest;
import com.estrecitas.dto.PerfilUtilizador;
import com.estrecitas.model.Utilizador;
import com.estrecitas.repository.UtilizadorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Serviço de negócio para gestão de utilizadores
 *
 * Este serviço fornece operações de alto nível para:
 * - Registo de novos utilizadores com validação
 * - Autenticação de utilizadores existentes
 * - Gestão de perfis de utilizador
 * - Operações CRUD básicas
 * - Validação de dados e regras de negócio
 *
 * Todas as operações são transacionais para garantir consistência
 * dos dados na base de dados.
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@Service
@Transactional
public class UtilizadorService {

    /**
     * Repositório para acesso aos dados de utilizadores
     */
    @Autowired
    private UtilizadorRepository utilizadorRepository;

    /**
     * Encoder para hash de passwords usando BCrypt
     */
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    // === OPERAÇÕES DE REGISTO E AUTENTICAÇÃO ===

    /**
     * Registar novo utilizador no sistema
     *
     * Valida os dados fornecidos, verifica se o email já existe,
     * hash a password e guarda o utilizador na base de dados.
     *
     * @param request dados do registo (nome, email, password, confirmação)
     * @return utilizador criado com ID gerado
     * @throws RuntimeException se email já existir ou passwords não coincidirem
     */
    public Utilizador registarUtilizador(RegistoRequest request) {
        // Verificar se email já está em uso
        if (utilizadorRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + request.getEmail());
        }

        // Verificar se passwords coincidem
        if (!request.isPasswordMatch()) {
            throw new RuntimeException("Passwords não coincidem");
        }

        // Criar novo utilizador com dados validados
        Utilizador utilizador = new Utilizador();
        utilizador.setNome(request.getNome());
        utilizador.setEmail(request.getEmail().toLowerCase()); // Normalizar email
        utilizador.setPassword(passwordEncoder.encode(request.getPassword())); // Hash da password

        // Guardar na base de dados
        return utilizadorRepository.save(utilizador);
    }
    
    /**
     * Autenticar utilizador
     */
    public Optional<Utilizador> authenticateUtilizador(String email, String password) {
        Optional<Utilizador> utilizadorOpt = utilizadorRepository.findByEmail(email.toLowerCase());
        
        if (utilizadorOpt.isPresent()) {
            Utilizador utilizador = utilizadorOpt.get();

            // Verificar password
            if (passwordEncoder.matches(password, utilizador.getPassword())) {
                utilizadorRepository.save(utilizador);
                return Optional.of(utilizador);
            }
        }
        
        return Optional.empty();
    }
    
    /**
     * Obter utilizador por ID
     */
    public Optional<Utilizador> getUtilizadorById(Long id) {
        return utilizadorRepository.findById(id);
    }
    
    /**
     * Obter utilizador por email
     */
    public Optional<Utilizador> getUtilizadorByEmail(String email) {
        return utilizadorRepository.findByEmail(email.toLowerCase());
    }
    
    /**
     * Obter perfil de utilizador
     */
    public Optional<PerfilUtilizador> getUtilizadorPerfil(Long id) {
        return utilizadorRepository.findById(id)
                .map(PerfilUtilizador::fromUtilizador);
    }
    
    /**
     * Atualizar perfil de utilizador
     */
    public Utilizador atualizarUtilizadorPerfil(Long id, String nome, String email) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        // Verificar se novo email já está em uso (por outro utilizador)
        if (!utilizador.getEmail().equals(email.toLowerCase())) {
            if (utilizadorRepository.existsByEmail(email.toLowerCase())) {
                throw new RuntimeException("Email já está em uso: " + email);
            }
        }
        
        utilizador.setNome(nome);
        utilizador.setEmail(email.toLowerCase());
        
        return utilizadorRepository.save(utilizador);
    }
    
    /**
     * Alterar password
     */
    public void changePassword(Long id, String currentPassword, String newPassword) {
        Utilizador utilizador = utilizadorRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Utilizador não encontrado"));
        
        // Verificar password atual
        if (!passwordEncoder.matches(currentPassword, utilizador.getPassword())) {
            throw new RuntimeException("Password atual incorreta");
        }
        
        // Atualizar password
        utilizador.setPassword(passwordEncoder.encode(newPassword));
        utilizadorRepository.save(utilizador);
    }
    
    /**
     * Eliminar utilizador
     */
    public void eliminarUtilizador(Long id) {
        if (!utilizadorRepository.existsById(id)) {
            throw new RuntimeException("Utilizador não encontrado");
        }
        utilizadorRepository.deleteById(id);
    }

    /**
     * Verificar se email existe
     */
    public boolean emailExists(String email) {
        return utilizadorRepository.existsByEmail(email.toLowerCase());
    }
}
