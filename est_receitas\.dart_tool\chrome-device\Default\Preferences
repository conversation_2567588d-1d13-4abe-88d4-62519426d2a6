{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "pt-BR"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1066, "left": 804, "maximized": true, "right": 1676, "top": 40, "work_area_bottom": 1019, "work_area_left": 0, "work_area_right": 1707, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 20564, "default_search_provider": {"choice_screen_random_shuffle_seed": "-3615591772005152232", "guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true,\"release-note\":true,\"issues-pane\":true}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "currentDockState": "\"right\"", "drawer-view-selected-tab": "\"release-note\"", "drawer-view-tab-order": "{\"console-view\":10,\"freestyler\":20,\"release-note\":30,\"issues-pane\":40}", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "inspectorVersion": "38", "panel-selected-tab": "\"console\"", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "a2faedc1-3c30-4577-bc84-61a73227a886", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "137.0.7151.104", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Descubra fantásticas aplicações, jogos, extensões e temas para o Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["metricsPrivate", "systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\136.0.7103.114\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.47764, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "9c50506a-6247-4d7b-b92e-c4d286aa93e7"}}, "history_clusters": {"all_cache": {"all_keywords": {}, "all_timestamp": "*****************"}, "short_cache": {"short_keywords": {}, "short_timestamp": "*****************"}}, "https_upgrade_navigations": {"2025-06-19": 51, "2025-06-20": 19}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13393************", "recent_session_start_times": ["*****************", "*****************", "**********0437834", "13393546310162552", "13393281172420548", "13393************"], "session_last_active_time": "13395081813127114", "session_start_time": "*****************"}, "intl": {"selected_languages": "pt-PT,pt,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 6}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "cwOkoFjm1nz+5eEoPQuYc3UL4g8GundOoYvWPUpy6mZQUGTI2J3KIC1OpLmnpSDRbbVQy3BSAZUjHeArgL369Q=="}, "ntp": {"num_personal_suggestions": 3}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13395081808118534", "last_fetch_success": "13395081808772530"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": true, "biometric_authentication_filling_promo_counter": 1, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:56254,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57645,*": {"expiration": "13402859596431716", "last_modified": "13395083596431721", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58747,*": {"expiration": "13401323175631877", "last_modified": "13393547175631883", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:59389,*": {"expiration": "13401037489866317", "last_modified": "13393261489866322", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:61926,*": {"expiration": "13401396228897921", "last_modified": "13393620228897927", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62398,*": {"expiration": "13402630659837078", "last_modified": "13394854659837084", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:8080,*": {"expiration": "13402630659879402", "last_modified": "13394854659879407", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "http://localhost:8081,*": {"expiration": "13401060832813802", "last_modified": "13393284832813808", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13395081798156312", "setting": {"lastEngagementTime": 1.3395033949612814e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.5}}, "http://localhost:56254,*": {"last_modified": "13395081798156305", "setting": {"lastEngagementTime": 1.3394920389675796e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:57645,*": {"last_modified": "13395081873067651", "setting": {"lastEngagementTime": 1.339508187306764e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.199999999999998, "rawScore": 7.199999999999998}}, "http://localhost:58747,*": {"last_modified": "13395081798156298", "setting": {"lastEngagementTime": 1.3394971147132648e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:59389,*": {"last_modified": "13395081798156292", "setting": {"lastEngagementTime": 1.3394921217820452e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}, "http://localhost:61926,*": {"last_modified": "13395081798156285", "setting": {"lastEngagementTime": 1.339500134147106e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 13.199999999999996}}, "http://localhost:62398,*": {"last_modified": "13395081798156277", "setting": {"lastEngagementTime": 1.3395052998156176e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 29.76000000000002}}, "http://localhost:8080,*": {"last_modified": "13395081798156268", "setting": {"lastEngagementTime": 1.3395051976424262e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 17.823840000000004}}, "http://localhost:8081,*": {"last_modified": "13395081798156221", "setting": {"lastEngagementTime": 1.3394942346955104e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.114", "creation_time": "13393259215106141", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13395081873067640", "last_time_obsolete_http_credentials_removed": **********.126231, "last_time_password_store_metrics_reported": **********.124686, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "O seu Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "D3340A34AF407A23BCA98AC0572E32FED6ADE0A01F7B92666210416B94F25F9E"}, "default_search_provider_data": {"template_url_data": "BBD06353FF0D611FF7906D1D86D025F3132F3F2DDADEB0FBCD47CBA4B7CC3B87"}, "enterprise_signin": {"policy_recovery_token": "8F54CBA5B1875BF136FDFA12DB8FCDF2322E1D3D5DA4925DD4AB24F5EBFCD895"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "0D9B27E89DEF89F8F58C3E6E3FBC4812816AC29C556E24918C8C3E07F3250DA8", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "6DB027018DC151FB634C22DA98C1E5321A58121F2094DC89F5825BD5CAF0AFDE", "neajdppkdcdipfabeoofebfddakdcjhd": "9ACA835422DA928BB8E2D3C0A202E32B5F237EEAF89D02EDFBD0C6FD088BAFE8", "nkeimhogjdpnpccoofpliimaahmaaome": "A536FE0B0EF01C7622DC24F653EA52B1B5F8A0FE32ECB212F78B574495846443"}, "ui": {"developer_mode": "809F9657EFF66E5647F25AE9F8233C3897BFFA480F768E47A6EB8E83BC4A328F"}}, "google": {"services": {"account_id": "FAE7DB01108AFEAC21EECFEBE29A9B4130BEACAEBD2E765F1C4503B44FE0FFC7", "last_signed_in_username": "7CCDE2FDA797BF6ACD2634063847FCEDBB7A7D4E274A2EFD0C177B9957074079", "last_username": "6B942F2594AB05DBA2FE865F83060727CD4A557B867A40948D0CE2E4D8EF5529"}}, "homepage": "B17C0B6AD0FD42AD7AC0424920A59F1E50F14D526021480C5BE9D5A5E3C34F0F", "homepage_is_newtabpage": "AF9EC2247DD0FC8086DE88BA0012469A01FCAEA595A022CD3C2C223797DF3DCA", "media": {"cdm": {"origin_data": "BA0EA9BFE46738A1351F7F910EDCCAE3698C7320927B71E355A12F27DFC341D0"}, "storage_id_salt": "5FD00D58E7D328BF4F772ADF8BD402D8B7D410A401477B3B50BF7B8C0C218142"}, "module_blocklist_cache_md5_digest": "2CEB266363B2A0DD81E0F6DFF1AB117CF314E14E491DC480E374009CE23D7E4B", "pinned_tabs": "A986494FA74A5E2E221A25387E7AC92754B8D875ED7569D46987201A4E4BD9F9", "prefs": {"preference_reset_time": "78BD85389449DFD410C41B98B38F6321FED9FDED702BB647DB6C5C880C1740EA"}, "safebrowsing": {"incidents_sent": "36206C3A0A03D783BA45D9756D72E8553367525C628C2DBB0FFF52C03D491A5D"}, "search_provider_overrides": "B33522C005F982C6F066E68DF69586BCE1B0FE5B5E231126B6FA8125D2079926", "session": {"restore_on_startup": "B1CCC5F6E7084F95A87F560BCF5B01BB1041FCDB68301ECF39DACA63266C2CCF", "startup_urls": "8797FD602EC64868F702B22051AE241C02B3BD569E6C6E2AEFFC11FC53D62905"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13395340998769739", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13395081798", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CmAKEXJlc3VtZV9oZWF2eV91c2VyEksKQA0AAAAAELqjq8Dz0OUXGi4KJgokDQAAAD8SFlJlc3VtZUhlYXZ5VXNlclNlZ21lbnQaBU90aGVyEgQQDhgEIAIQxaOrwPPQ5RcKcwoVcGFzc3dvcmRfbWFuYWdlcl91c2VyEloKTw0AAAAAEJykq8Dz0OUXGj0KNQozDQAAAD8SE1Bhc3N3b3JkTWFuYWdlclVzZXIaF05vdF9QYXNzd29yZE1hbmFnZXJVc2VyEgQQBxgEIAEQraSrwPPQ5RcKZAoLc2VhcmNoX3VzZXISVQpKDQAAAAAQw6SrwPPQ5RcaOAowGi4KCg0AAIA/EgNMb3cKDQ0AAKBAEgZNZWRpdW0KCw0AALBBEgRIaWdoEgROb25lEgQQBxgEIAIQzaSrwPPQ5RcKUgoNc2hvcHBpbmdfdXNlchJBCjYNAAAAABDklfmkmNjlFxokChwKGg0AAAA/EgxTaG9wcGluZ1VzZXIaBU90aGVyEgQQAhgEIAMQh5b5pJjY5RcK5QIKEWNyb3NzX2RldmljZV91c2VyEs8CCsMCDQAAgD8QgaOrwPPQ5RcasAIKpwIapAIKGQ0AAIA/EhJOb0Nyb3NzRGV2aWNlVXNhZ2UKGA0AAABAEhFDcm9zc0RldmljZU1vYmlsZQoZDQAAQEASEkNyb3NzRGV2aWNlRGVza3RvcAoYDQAAgEASEUNyb3NzRGV2aWNlVGFibGV0CiINAACgQBIbQ3Jvc3NEZXZpY2VNb2JpbGVBbmREZXNrdG9wCiENAADAQBIaQ3Jvc3NEZXZpY2VNb2JpbGVBbmRUYWJsZXQKIg0AAOBAEhtDcm9zc0RldmljZURlc2t0b3BBbmRUYWJsZXQKIA0AAABBEhlDcm9zc0RldmljZUFsbERldmljZVR5cGVzChcNAAAQQRIQQ3Jvc3NEZXZpY2VPdGhlchISTm9Dcm9zc0RldmljZVVzYWdlEgQQBxgEIAIQm6OrwPPQ5Rc=", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13394937599000000", "uma_in_sql_start_time": "13393259215123770"}, "sessions": {"event_log": [{"crashed": false, "time": "13393259215122476", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393259326414853", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393260043336408", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393261489858504", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393281172407646", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393284832808949", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393546310153459", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393547175627517", "type": 2, "window_count": 1}, {"crashed": false, "time": "**********0424280", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13393620228892872", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394831405294182", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 3, "time": "13394854659828669", "type": 2, "window_count": 3}, {"crashed": false, "time": "13395081798117982", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395083596427852", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"PasswordSignInPromoShownCount": 1, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["pt-PT"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_ignored_count_for_language": {"en": 5}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"lar são mamede infesta\",\"exame historia\",\"cinema madeira shopping\",\"nicolás figal\",\"acidente comboio intercidades\",\"euromilhões jackpot\",\"ps6\",\"taxa selic hoje\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChwIkk4SFwoTUGVzcXVpc2FzIHBvcHVsYXJlcygK\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\",\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"-6425679942647392906\",\"google:suggestrelevance\":[1250,1202,1201,1200,1153,1152,1151,1150],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}