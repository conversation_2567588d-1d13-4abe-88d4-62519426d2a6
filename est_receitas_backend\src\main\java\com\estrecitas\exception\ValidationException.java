package com.estrecitas.exception;

import java.util.List;
import java.util.Map;

/**
 * Exceção quando ocorrem erros de validação nos dados recebidos.
 * Permite armazenar múltiplos erros por campo para fornecer feedback detalhado.
 */

public class ValidationException extends RuntimeException {
    
    private Map<String, List<String>> errors;
    
    public ValidationException(String message) {
        super(message);
    }
    
    public ValidationException(String message, Map<String, List<String>> errors) {
        super(message);
        this.errors = errors;
    }
    
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public Map<String, List<String>> getErrors() {
        return errors;
    }
    
    public void setErrors(Map<String, List<String>> errors) {
        this.errors = errors;
    }
}
