package com.estrecitas.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * DTO para pedido de registo
 */
public class RegistoRequest {
    
    @NotBlank(message = "O nome é obrigatório")
    @Size(min = 2, max = 100, message = "O nome deve ter entre 2 e 100 caracteres")
    private String nome;
    
    @NotBlank(message = "O email é obrigatório")
    @Email(message = "O email deve ter formato válido")
    @Size(max = 150, message = "O email deve ter no máximo 150 caracteres")
    private String email;
    
    @NotBlank(message = "A password é obrigatória")
    @Size(min = 6, max = 50, message = "A password deve ter entre 6 e 50 caracteres")
    private String password;
    
    @NotBlank(message = "Confirmação de password é obrigatória")
    private String confirmPassword;
    
    // Construtores
    public RegistoRequest() {}
    
    public RegistoRequest(String nome, String email, String password, String confirmPassword) {
        this.nome = nome;
        this.email = email;
        this.password = password;
        this.confirmPassword = confirmPassword;
    }
    
    // Getters e Setters
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getConfirmPassword() {
        return confirmPassword;
    }
    
    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }
    
    // Métodos auxiliares
    public boolean isPasswordMatch() {
        return password != null && password.equals(confirmPassword);
    }
    
    @Override
    public String toString() {
        return "RegistoRequest{" +
                "nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                ", password='[PROTECTED]'" +
                ", confirmPassword='[PROTECTED]'" +
                '}';
    }
}
