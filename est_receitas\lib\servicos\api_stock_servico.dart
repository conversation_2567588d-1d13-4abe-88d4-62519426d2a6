import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/item_despensa.dart';
import '../config/api_config.dart';

/// Serviço de API para comunicação com o backend Spring Boot - Stock
class ApiStockServico {

  // ===== OPERAÇÕES BÁSICAS =====

  /// Obter todos os itens
  Future<List<ItemDespensa>> obterTodosItens() async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET ${ApiConfig.stockUrl}');
      }

      final response = await http.get(
        Uri.parse(ApiConfig.stockUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (ApiConfig.enableLogging) {
        debugPrint('📡 Response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final itens = data.map((json) => _itemFromBackendJson(json)).toList();

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Itens obtidos: ${itens.length}');
        }

        return itens;
      } else {
        throw Exception('Erro ao obter itens: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao obter itens: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Obter item por ID
  Future<ItemDespensa?> obterItemPorId(int id) async {
    try {
      final url = ApiConfig.stockItemPorId(id);

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 GET $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _itemFromBackendJson(data);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception('Erro ao obter item: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao obter item por ID: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Adicionar novo item
  Future<ItemDespensa> adicionarItem(ItemDespensa item) async {
    try {
      final body = json.encode(_itemToBackendJson(item));

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 POST ${ApiConfig.stockUrl}');
        if (ApiConfig.logRequestBody) {
          debugPrint('📤 Body: $body');
        }
      }

      final response = await http.post(
        Uri.parse(ApiConfig.stockUrl),
        headers: ApiConfig.jsonHeaders,
        body: body,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _itemFromBackendJson(data);
      } else {
        if (ApiConfig.enableLogging) {
          debugPrint('❌ Erro ${response.statusCode}: ${response.body}');
        }
        throw Exception('Erro ao adicionar item: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao adicionar item: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Atualizar item
  Future<ItemDespensa> atualizarItem(ItemDespensa item) async {
    if (item.id == null) {
      throw Exception('ID do item é obrigatório para atualização');
    }

    try {
      final url = ApiConfig.stockItemPorId(item.id!);
      final body = json.encode(_itemToBackendJson(item));

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 PUT $url');
      }

      final response = await http.put(
        Uri.parse(url),
        headers: ApiConfig.jsonHeaders,
        body: body,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return _itemFromBackendJson(data);
      } else {
        throw Exception('Erro ao atualizar item: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao atualizar item: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Remover item
  Future<bool> removerItem(int id) async {
    try {
      final url = ApiConfig.stockItemPorId(id);

      if (ApiConfig.enableLogging) {
        debugPrint('🌐 DELETE $url');
      }

      final response = await http.delete(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      return response.statusCode == 204;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro ao remover item: $e');
      }
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== OPERAÇÕES POR LOCALIZAÇÃO =====

  /// Obter itens da despensa
  Future<List<ItemDespensa>> obterItensDespensa() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.stockDespensaUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _itemFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao obter itens da despensa: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Obter itens do frigorifico
  Future<List<ItemDespensa>> obterItensFrigorifico() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.stockFrigorificoUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _itemFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao obter itens do frigorifico: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Erro de conectividade: $e');
    }
  }

  /// Obter itens por localização
  Future<List<ItemDespensa>> obterItensPorLocalizacao(LocalizacaoItem localizacao) async {
    final endpoint = localizacao == LocalizacaoItem.despensa ? 'despensa' : 'frigorifico';

    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.stockUrl}/$endpoint'),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _itemFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao obter itens por localização: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== OPERAÇÕES DE PESQUISA =====

  /// Pesquisar itens por nome
  Future<List<ItemDespensa>> pesquisarItensPorNome(String nome) async {
    try {
      final url = ApiConfig.buildUrl(ApiConfig.stockPesquisarUrl, {'nome': nome});

      final response = await http.get(
        Uri.parse(url),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _itemFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao pesquisar itens: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== GESTÃO DE VALIDADES =====

  /// Obter itens vencidos
  Future<List<ItemDespensa>> obterItensVencidos() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.stockVencidosUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.receiveTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => _itemFromBackendJson(json)).toList();
      } else {
        throw Exception('Erro ao obter itens vencidos: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Erro de conectividade: $e');
    }
  }

  // ===== OPERAÇÕES AUXILIARES =====

  /// Verificar conectividade com a API
  Future<bool> verificarConectividade() async {
    try {
      final response = await http.get(
        Uri.parse(ApiConfig.verificarSaudeUrl),
        headers: ApiConfig.defaultHeaders,
      ).timeout(ApiConfig.connectTimeout);

      return response.statusCode == 200;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Erro de conectividade: $e');
      }
      return false;
    }
  }

  // ===== MÉTODOS PRIVADOS DE CONVERSÃO =====

  /// Converte item do backend para modelo Flutter
  ItemDespensa _itemFromBackendJson(Map<String, dynamic> json) {
    return ItemDespensa(
      id: json['id'],
      nome: json['nome'] ?? '',
      quantidade: json['quantidade']?.toDouble() ?? 0.0,
      unidade: json['unidade'] ?? '',
      categoria: json['categoria'],
      dataValidade: json['dataValidade'] != null ? DateTime.parse(json['dataValidade']) : null,
      localizacao: _parseLocalizacao(json['localizacao']),
    );
  }

  /// Converte item do Flutter para formato do backend
  Map<String, dynamic> _itemToBackendJson(ItemDespensa item) {
    return {
      if (item.id != null) 'id': item.id,
      'nome': item.nome,
      'quantidade': item.quantidade,
      'unidade': item.unidade,
      if (item.categoria != null) 'categoria': item.categoria,
      if (item.dataValidade != null) 'dataValidade': item.dataValidade!.toIso8601String().split('T')[0],
      'localizacao': _localizacaoToBackend(item.localizacao),
    };
  }

  /// Converte localização do backend para enum Flutter
  LocalizacaoItem _parseLocalizacao(String? localizacao) {
    switch (localizacao?.toUpperCase()) {
      case 'DESPENSA':
        return LocalizacaoItem.despensa;
      case 'FRIGORIFICO':
        return LocalizacaoItem.frigorifico;
      default:
        return LocalizacaoItem.despensa;
    }
  }

  /// Converte enum Flutter para formato do backend
  String _localizacaoToBackend(LocalizacaoItem localizacao) {
    switch (localizacao) {
      case LocalizacaoItem.despensa:
        return 'DESPENSA';
      case LocalizacaoItem.frigorifico:
        return 'FRIGORIFICO';
    }
  }
}
