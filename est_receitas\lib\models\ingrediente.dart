// Modelo de dados para um ingrediente
class Ingrediente {
  final int? id;
  final String nome;
  final double quantidade;
  final String unidade;
  final int? receitaId;

  const Ingrediente({
    this.id,
    required this.nome,
    required this.quantidade,
    required this.unidade,
    this.receitaId,
  });

  // Converte o ingrediente para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'receitaId': receitaId,
    };
  }

  // Converte o ingrediente para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'receitaId': receitaId,
    };
  }

  // Cria um ingrediente a partir de um Map (armazenamento local)
  factory Ingrediente.fromMap(Map<String, dynamic> map) {
    return Ingrediente(
      id: map['id'],
      nome: map['nome'] ?? '',
      quantidade: map['quantidade']?.toDouble() ?? 0.0,
      unidade: map['unidade'] ?? '',
      receitaId: map['receitaId'],
    );
  }

  // Cria um ingrediente a partir de JSON (API)
  factory Ingrediente.fromJson(Map<String, dynamic> json) {
    return Ingrediente(
      id: json['id'],
      nome: json['nome'] ?? '',
      quantidade: json['quantidade']?.toDouble() ?? 0.0,
      unidade: json['unidade'] ?? '',
      receitaId: json['receitaId'],
    );
  }

  @override
  String toString() {
    return 'Ingrediente(id: $id, nome: $nome, quantidade: $quantidade, unidade: $unidade)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Ingrediente &&
      other.id == id &&
      other.nome == nome &&
      other.quantidade == quantidade &&
      other.unidade == unidade &&
      other.receitaId == receitaId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      nome.hashCode ^
      quantidade.hashCode ^
      unidade.hashCode ^
      receitaId.hashCode;
  }
}