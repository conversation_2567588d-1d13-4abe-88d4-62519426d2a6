/// Modelo de dados para um ingrediente
class Ingrediente {
  final int? id;
  final String nome;
  final double quantidade;
  final String unidade;
  final int? receitaId;

  const Ingrediente({
    this.id,
    required this.nome,
    required this.quantidade,
    required this.unidade,
    this.receitaId,
  });

  /// Cria uma cópia do ingrediente com campos alterados
  Ingrediente copyWith({
    int? id,
    String? nome,
    double? quantidade,
    String? unidade,
    int? receitaId,
  }) {
    return Ingrediente(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      quantidade: quantidade ?? this.quantidade,
      unidade: unidade ?? this.unidade,
      receitaId: receitaId ?? this.receitaId,
    );
  }

  /// Converte o ingrediente para Map (para armazenamento local)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'receitaId': receitaId,
    };
  }

  /// Converte o ingrediente para JSON (para API)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'quantidade': quantidade,
      'unidade': unidade,
      'receitaId': receitaId,
    };
  }

  /// Cria um ingrediente a partir de um Map (armazenamento local)
  factory Ingrediente.fromMap(Map<String, dynamic> map) {
    return Ingrediente(
      id: map['id'],
      nome: map['nome'] ?? '',
      quantidade: map['quantidade']?.toDouble() ?? 0.0,
      unidade: map['unidade'] ?? '',
      receitaId: map['receitaId'],
    );
  }

  /// Cria um ingrediente a partir de JSON (API)
  factory Ingrediente.fromJson(Map<String, dynamic> json) {
    return Ingrediente(
      id: json['id'],
      nome: json['nome'] ?? '',
      quantidade: json['quantidade']?.toDouble() ?? 0.0,
      unidade: json['unidade'] ?? '',
      receitaId: json['receitaId'],
    );
  }

  /// Retorna uma representação legível do ingrediente
  String get textoExibicao {
    String texto = '';
    
    // Formatar quantidade
    if (quantidade == quantidade.toInt()) {
      texto = '${quantidade.toInt()}';
    } else {
      texto = quantidade.toStringAsFixed(1);
    }
    
    // Adicionar unidade
    if (unidade.isNotEmpty) {
      texto += ' $unidade';
    }
    
    // Adicionar nome
    if (nome.isNotEmpty) {
      texto += ' de $nome';
    }
        
    return texto;
  }

  /// Retorna quantidade formatada
  String get quantidadeFormatada {
    if (quantidade == quantidade.toInt()) {
      return quantidade.toInt().toString();
    } else {
      return quantidade.toStringAsFixed(1);
    }
  }

  @override
  String toString() {
    return 'Ingrediente(id: $id, nome: $nome, quantidade: $quantidade, unidade: $unidade)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    // Comparação completa de todos os atributos do ingrediente
    // Dois ingredientes são considerados iguais se todos os seus campos forem iguais
    return other is Ingrediente &&
      other.id == id &&
      other.nome == nome &&
      other.quantidade == quantidade &&
      other.unidade == unidade &&
      other.receitaId == receitaId;
  }

  @override
  int get hashCode {
    // Gera um código hash único para o ingrediente,
    // combinando os valores hash de todos os campos usando operador XOR (^)
    // Isso garante uma distribuição adequada de valores hash para coleções
    return id.hashCode ^
      nome.hashCode ^
      quantidade.hashCode ^
      unidade.hashCode ^
      receitaId.hashCode;
  }
}
