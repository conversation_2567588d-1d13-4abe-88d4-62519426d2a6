package com.estrecitas.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Entidade JPA que representa um utilizador da aplicação EST Receitas
 *
 * Esta classe mapeia para a tabela 'users' na base de dados e contém
 * as informações básicas de um utilizador:
 * - Identificador único (ID)
 * - Nome completo
 * - Email (único, usado para login)
 * - Password (hash BCrypt)
 *
 * A entidade é usada para:
 * - Autenticação e autorização
 * - Gestão de perfis de utilizador
 * - Associação com receitas e itens de stock
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@Entity
@Table(name = "users")
public class Utilizador {

    /**
     * Identificador único do utilizador (chave primária)
     * Gerado automaticamente pela base de dados
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Nome completo do utilizador
     * Campo obrigatório com máximo de 100 caracteres
     */
    @Column(nullable = false, length = 100)
    private String nome;

    /**
     * Email do utilizador (usado para login)
     * Campo obrigatório, único e com máximo de 150 caracteres
     */
    @Column(nullable = false, unique = true, length = 150)
    private String email;

    /**
     * Password do utilizador (hash BCrypt)
     * Campo obrigatório com máximo de 255 caracteres para acomodar o hash
     */
    @Column(nullable = false, length = 255)
    private String password;
    
    // === CONSTRUTORES ===

    /**
     * Construtor padrão necessário para JPA
     * Usado pelo Hibernate para criar instâncias da entidade
     */
    public Utilizador() {
        // Construtor vazio necessário para JPA
    }

    /**
     * Construtor com parâmetros para criar um novo utilizador
     *
     * @param nome nome completo do utilizador
     * @param email email único do utilizador
     * @param password password em texto plano (será hash pelo serviço)
     */
    public Utilizador(String nome, String email, String password) {
        this.nome = nome;
        this.email = email;
        this.password = password;
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }

    
    // equals e hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Utilizador utilizador = (Utilizador) o;
        return Objects.equals(id, utilizador.id) && 
               Objects.equals(email, utilizador.email);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, email);
    }
    
    // toString
    @Override
    public String toString() {
        return "Utilizador{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
