import 'package:flutter/material.dart';

// Constantes da aplicação
class AppConstants {
  // Cores da aplicação
  static const Color primaryColor = Color(0xFF2E7D32); // Verde escuro
  static const Color secondaryColor = Color(0xFF4CAF50); // Verde claro
  static const Color accentColor = Color(0xFFFF9800); // Laranja
  static const Color backgroundColor = Color(0xFFF5F5F5); // Cinza claro
  static const Color errorColor = Color(0xFFD32F2F); // Vermelho
  static const Color warningColor = Color(0xFFFF9800); // Laranja
  static const Color successColor = Color(0xFF4CAF50); // Verde

  // Cores específicas para diferentes secções
  static const Color despensaColor = Color(0xFF8BC34A); // Verde claro para despensa
  static const Color frigorifico = Color(0xFF2196F3); // Azul para frigorifico
  static const Color receitasColor = Color(0xFFFF5722); // Laranja avermelhado para receitas

  // Tamanhos de texto
  static const double titleFontSize = 24.0;
  static const double subtitleFontSize = 18.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  static const double smallFontSize = 12.0;

  // Espaçamentos
  static const double smallPadding = 8.0;
  static const double mediumPadding = 16.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  // Raios de borda
  static const double smallBorderRadius = 4.0;
  static const double mediumBorderRadius = 8.0;
  static const double largeBorderRadius = 12.0;
  static const double extraLargeBorderRadius = 16.0;

  // Elevações
  static const double lowElevation = 2.0;
  static const double mediumElevation = 4.0;
  static const double highElevation = 8.0;

  // Durações de animação
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Textos genéricos
  static const String appName = 'EST Receitas';
  static const String appVersion = '1.0.0';

  // Mensagens de erro
  static const String errorGenerico = 'Ocorreu um erro inesperado';
  static const String errorInternet = 'Verifique a sua ligação à internet';
  static const String errorCarregamento = 'Erro ao carregar dados';
  static const String errorGuardar = 'Erro ao guardar dados';

  // Mensagens de sucesso
  static const String sucessoGuardar = 'Dados guardados com sucesso';
  static const String sucessoRemover = 'Item removido com sucesso';
  static const String sucessoActualizar = 'Dados actualizados com sucesso';

  // Limites
  static const int maxReceitasTitulo = 100;
  static const int maxReceitasDescricao = 500;
  static const int maxIngredienteNome = 50;
  static const int maxItemDespensaNome = 50;
  static const double maxQuantidade = 9999.99;
  static const int diasAvisoValidade = 3;

  // Chaves de armazenamento
  static const String keyReceitas = 'receitas';
  static const String keyItensDespensa = 'itens_despensa';
  static const String keyConfiguracoes = 'configuracoes';
  static const String keyPrimeiraVez = 'primeira_vez';
}

// Extensão para adicionar cores personalizadas ao Colors
extension CustomColors on Colors {
  static const Color myColor1 = AppConstants.primaryColor;
  static const Color myColor2 = AppConstants.secondaryColor;
  static const Color despensa = AppConstants.despensaColor;
  static const Color frigorifico = AppConstants.frigorifico;
  static const Color receitas = AppConstants.receitasColor;
}

// Tema da aplicação
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Roboto',
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
      ),
      // Garantir que os ícones do Material Design são incluídos
      iconTheme: const IconThemeData(
        color: Colors.black87,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: AppConstants.lowElevation,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      cardTheme: CardTheme(
        elevation: AppConstants.mediumElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.mediumPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.mediumPadding),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Roboto',
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.dark,
      ),
      // Garantir que os ícones do Material Design são incluídos
      iconTheme: const IconThemeData(
        color: Colors.white70,
      ),
      appBarTheme: const AppBarTheme(
        centerTitle: true,
        elevation: AppConstants.lowElevation,
        iconTheme: IconThemeData(color: Colors.white),
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      cardTheme: CardTheme(
        elevation: AppConstants.mediumElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.largePadding,
            vertical: AppConstants.mediumPadding,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.mediumBorderRadius),
        ),
        contentPadding: const EdgeInsets.all(AppConstants.mediumPadding),
      ),
    );
  }
}

// Estilos de texto personalizados
class AppTextStyles {
  static const TextStyle title = TextStyle(
    fontSize: AppConstants.titleFontSize,
    fontWeight: FontWeight.bold,
  );

  static const TextStyle subtitle = TextStyle(
    fontSize: AppConstants.subtitleFontSize,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle body = TextStyle(
    fontSize: AppConstants.bodyFontSize,
  );

  static const TextStyle caption = TextStyle(
    fontSize: AppConstants.captionFontSize,
    color: Colors.grey,
  );

  static const TextStyle small = TextStyle(
    fontSize: AppConstants.smallFontSize,
    color: Colors.grey,
  );
}
