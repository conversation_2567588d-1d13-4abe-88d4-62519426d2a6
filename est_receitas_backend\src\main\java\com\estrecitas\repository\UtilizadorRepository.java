package com.estrecitas.repository;

import com.estrecitas.model.Utilizador;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repositório JPA para a entidade Utilizador
 *
 * Estende JpaRepository para fornecer operações CRUD básicas
 * e define métodos de consulta personalizados para:
 * - Busca por email (login)
 * - Verificação de existência de email (registo)
 * - Pesquisa de utilizadores (administração)
 *
 * O Spring Data JPA gera automaticamente a implementação
 * dos métodos baseado nos nomes e anotações @Query.
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@Repository
public interface UtilizadorRepository extends JpaRepository<Utilizador, Long> {

    /**
     * Encontra um utilizador pelo seu email
     *
     * Usado principalmente para autenticação durante o login.
     *
     * @param email email do utilizador
     * @return Optional contendo o utilizador se encontrado
     */
    Optional<Utilizador> findByEmail(String email);

    /**
     * Verifica se já existe um utilizador com o email especificado
     *
     * Usado durante o registo para evitar emails duplicados.
     *
     * @param email email a verificar
     * @return true se o email já existir, false caso contrário
     */
    boolean existsByEmail(String email);

    /**
     * Pesquisa utilizadores por email (busca parcial, case-insensitive)
     *
     * Usado para funcionalidades de pesquisa e administração.
     * A query JPQL usa LOWER() e LIKE com wildcards para busca flexível.
     *
     * @param email parte do email a pesquisar
     * @return lista de utilizadores que correspondem ao critério
     */
    @Query("SELECT u FROM Utilizador u WHERE LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))")
    List<Utilizador> findByEmailContainingIgnoreCase(@Param("email") String email);
}
