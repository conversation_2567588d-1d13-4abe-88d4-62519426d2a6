package com.estrecitas.repository;

import com.estrecitas.model.Utilizador;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository para entidade Utilizador
 */
@Repository
public interface UtilizadorRepository extends JpaRepository<Utilizador, Long> {
    
    /**
     * Encontrar utilizador por email
     */
    Optional<Utilizador> findByEmail(String email);
    
    /**
     * Verificar se email já existe
     */
    boolean existsByEmail(String email);
    
    /**
     * Pesquisar utilizadores por email
     */
    @Query("SELECT u FROM Utilizador u WHERE LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%')) AND u.ativo = true")
    List<Utilizador> findByEmailContainingIgnoreCaseAndAtivoTrue(@Param("email") String email);
}
