package com.estrecitas.repository;

import com.estrecitas.model.Utilizador;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository para entidade Utilizador
 */
@Repository
public interface UtilizadorRepository extends JpaRepository<Utilizador, Long> {
    
    /**
     * Encontrar utilizador por email
     */
    Optional<Utilizador> findByEmail(String email);
    
    /**
     * Verificar se email já existe
     */
    boolean existsByEmail(String email);
    
    /**
     * Encontrar utilizadores ativos
     */
    List<Utilizador> findByAtivoTrue();
    
    // Métodos relacionados com role removidos - campo não existe na entidade Utilizador
    
    /**
     * Encontrar utilizadores criados após uma data
     */
    List<Utilizador> findByDataCriacaoAfter(LocalDateTime data);
    
    /**
     * Encontrar utilizadores que fizeram login após uma data
     */
    List<Utilizador> findByDataUltimoLoginAfter(LocalDateTime data);
    
    /**
     * Contar utilizadores ativos
     */
    long countByAtivoTrue();
    
    /**
     * Pesquisar utilizadores por nome
     */
    @Query("SELECT u FROM Utilizador u WHERE LOWER(u.nome) LIKE LOWER(CONCAT('%', :nome, '%')) AND u.ativo = true")
    List<Utilizador> findByNomeContainingIgnoreCaseAndAtivoTrue(@Param("nome") String nome);
    
    /**
     * Pesquisar utilizadores por email
     */
    @Query("SELECT u FROM Utilizador u WHERE LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%')) AND u.ativo = true")
    List<Utilizador> findByEmailContainingIgnoreCaseAndAtivoTrue(@Param("email") String email);
    
    /**
     * Encontrar utilizadores inativos há mais de X dias
     */
    @Query("SELECT u FROM Utilizador u WHERE u.dataUltimoLogin < :dataLimite OR u.dataUltimoLogin IS NULL")
    List<Utilizador> findInactiveUsers(@Param("dataLimite") LocalDateTime dataLimite);
    
    /**
     * Estatísticas de utilizadores por mês
     */
    @Query("SELECT YEAR(u.dataCriacao) as ano, MONTH(u.dataCriacao) as mes, COUNT(u) as total " +
           "FROM Utilizador u " +
           "GROUP BY YEAR(u.dataCriacao), MONTH(u.dataCriacao) " +
           "ORDER BY ano DESC, mes DESC")
    List<Object[]> getUtilizadorStatisticsByMonth();
    
    /**
     * Utilizadores mais ativos
     */
    @Query("SELECT u FROM Utilizador u WHERE u.dataUltimoLogin IS NOT NULL " +
           "ORDER BY u.dataUltimoLogin DESC")
    List<Utilizador> findMostActiveUsers();
}
