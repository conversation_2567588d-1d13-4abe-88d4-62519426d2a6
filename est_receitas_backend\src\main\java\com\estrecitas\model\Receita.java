package com.estrecitas.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidade JPA que representa uma receita culinária na aplicação EST Receitas
 *
 * Esta classe mapeia para a tabela 'receitas' na base de dados e contém
 * todas as informações de uma receita:
 * - Informações básicas (título, descrição, instruções)
 * - Metadados (tempo de preparo, porções, dificuldade)
 * - Relacionamentos (ingredientes)
 * - Recursos multimédia (URL da imagem)
 *
 * A entidade estabelece um relacionamento One-to-Many com Ingrediente,
 * permitindo que cada receita tenha múltiplos ingredientes associados.
 *
 * <AUTHOR> Receitas Team
 * @version 1.0.0
 * @since 2024
 */
@Entity
@Table(name = "receitas")
public class Receita {

    /**
     * Identificador único da receita (chave primária)
     * Gerado automaticamente pela base de dados
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Título da receita
     * Campo obrigatório com máximo de 200 caracteres
     */
    @Column(nullable = false, length = 200)
    private String titulo;

    /**
     * Descrição detalhada da receita
     * Campo de texto longo opcional
     */
    @Column(columnDefinition = "TEXT")
    private String descricao;

    /**
     * Instruções passo-a-passo para preparar a receita
     * Campo de texto longo opcional
     */
    @Column(columnDefinition = "TEXT")
    private String instrucoes;

    /**
     * Tempo estimado de preparo em minutos
     * Campo opcional
     */
    @Column(name = "tempo_preparo")
    private Integer tempoPreparo;

    /**
     * Número de porções que a receita produz
     * Campo opcional
     */
    @Column(name = "numero_porcoes")
    private Integer numeroPorcoes;

    /**
     * Nível de dificuldade da receita
     * Enum com valores: FACIL, MEDIO, DIFICIL
     */
    @Column(name = "dificuldade")
    @Enumerated(EnumType.STRING)
    private DificuldadeReceita dificuldade;

    /**
     * Categoria da receita (ex: sobremesa, prato principal, entrada)
     * Campo opcional
     */
    @Column(name = "categoria")
    private String categoria;

    /**
     * URL da imagem da receita
     * Campo opcional para recursos multimédia
     */
    @Column(name = "imagem_url")
    private String imagemUrl;

    /**
     * Lista de ingredientes da receita
     * Relacionamento One-to-Many com cascade ALL e fetch LAZY
     * Os ingredientes são carregados apenas quando necessário
     */
    @OneToMany(mappedBy = "receita", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Ingrediente> ingredientes = new ArrayList<>();
    
    // Construtores
    public Receita() {
    }
    
    public Receita(String titulo, String descricao, String instrucoes) {
        this();
        this.titulo = titulo;
        this.descricao = descricao;
        this.instrucoes = instrucoes;
    }
    

    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitulo() {
        return titulo;
    }
    
    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }
    
    public String getDescricao() {
        return descricao;
    }
    
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public String getInstrucoes() {
        return instrucoes;
    }
    
    public void setInstrucoes(String instrucoes) {
        this.instrucoes = instrucoes;
    }
    
    public Integer getTempoPreparo() {
        return tempoPreparo;
    }
    
    public void setTempoPreparo(Integer tempoPreparo) {
        this.tempoPreparo = tempoPreparo;
    }
    
    public Integer getNumeroPorcoes() {
        return numeroPorcoes;
    }
    
    public void setNumeroPorcoes(Integer numeroPorcoes) {
        this.numeroPorcoes = numeroPorcoes;
    }
    
    public DificuldadeReceita getDificuldade() {
        return dificuldade;
    }
    
    public void setDificuldade(DificuldadeReceita dificuldade) {
        this.dificuldade = dificuldade;
    }
    
    public String getCategoria() {
        return categoria;
    }
    
    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
    
    public String getImagemUrl() {
        return imagemUrl;
    }
    
    public void setImagemUrl(String imagemUrl) {
        this.imagemUrl = imagemUrl;
    }
    
    public List<Ingrediente> getIngredientes() {
        return ingredientes;
    }
    
    public void setIngredientes(List<Ingrediente> ingredientes) {
        this.ingredientes = ingredientes;
    }
    
    // Métodos auxiliares
    public void adicionarIngrediente(Ingrediente ingrediente) {
        ingredientes.add(ingrediente);
        ingrediente.setReceita(this);
    }
    
    public void removerIngrediente(Ingrediente ingrediente) {
        ingredientes.remove(ingrediente);
        ingrediente.setReceita(null);
    }
    
    @Override
    public String toString() {
        return "Receita{" +
                "id=" + id +
                ", titulo='" + titulo + '\'' +
                ", categoria='" + categoria + '\'' +
                ", tempoPreparo=" + tempoPreparo +
                ", numeroPorcoes=" + numeroPorcoes +
                '}';
    }
}
