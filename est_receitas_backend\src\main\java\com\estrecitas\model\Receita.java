package com.estrecitas.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "receitas")
public class Receita {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 200)
    private String titulo;
    
    @Column(columnDefinition = "TEXT")
    private String descricao;
    
    @Column(columnDefinition = "TEXT")
    private String instrucoes;
    
    @Column(name = "tempo_preparo")
    private Integer tempoPreparo; // em minutos
    
    @Column(name = "numero_porcoes")
    private Integer numeroPorcoes;
    
    @Column(name = "dificuldade")
    @Enumerated(EnumType.STRING)
    private DificuldadeReceita dificuldade;
    
    @Column(name = "categoria")
    private String categoria;
    
    @Column(name = "imagem_url")
    private String imagemUrl;
    
    @OneToMany(mappedBy = "receita", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Ingrediente> ingredientes = new ArrayList<>();
    
    // Construtores
    public Receita() {
    }
    
    public Receita(String titulo, String descricao, String instrucoes) {
        this();
        this.titulo = titulo;
        this.descricao = descricao;
        this.instrucoes = instrucoes;
    }
    

    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitulo() {
        return titulo;
    }
    
    public void setTitulo(String titulo) {
        this.titulo = titulo;
    }
    
    public String getDescricao() {
        return descricao;
    }
    
    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
    
    public String getInstrucoes() {
        return instrucoes;
    }
    
    public void setInstrucoes(String instrucoes) {
        this.instrucoes = instrucoes;
    }
    
    public Integer getTempoPreparo() {
        return tempoPreparo;
    }
    
    public void setTempoPreparo(Integer tempoPreparo) {
        this.tempoPreparo = tempoPreparo;
    }
    
    public Integer getNumeroPorcoes() {
        return numeroPorcoes;
    }
    
    public void setNumeroPorcoes(Integer numeroPorcoes) {
        this.numeroPorcoes = numeroPorcoes;
    }
    
    public DificuldadeReceita getDificuldade() {
        return dificuldade;
    }
    
    public void setDificuldade(DificuldadeReceita dificuldade) {
        this.dificuldade = dificuldade;
    }
    
    public String getCategoria() {
        return categoria;
    }
    
    public void setCategoria(String categoria) {
        this.categoria = categoria;
    }
    
    public String getImagemUrl() {
        return imagemUrl;
    }
    
    public void setImagemUrl(String imagemUrl) {
        this.imagemUrl = imagemUrl;
    }
    
    public List<Ingrediente> getIngredientes() {
        return ingredientes;
    }
    
    public void setIngredientes(List<Ingrediente> ingredientes) {
        this.ingredientes = ingredientes;
    }
    
    // Métodos auxiliares
    public void adicionarIngrediente(Ingrediente ingrediente) {
        ingredientes.add(ingrediente);
        ingrediente.setReceita(this);
    }
    
    public void removerIngrediente(Ingrediente ingrediente) {
        ingredientes.remove(ingrediente);
        ingrediente.setReceita(null);
    }
    
    @Override
    public String toString() {
        return "Receita{" +
                "id=" + id +
                ", titulo='" + titulo + '\'' +
                ", categoria='" + categoria + '\'' +
                ", tempoPreparo=" + tempoPreparo +
                ", numeroPorcoes=" + numeroPorcoes +
                '}';
    }
}
