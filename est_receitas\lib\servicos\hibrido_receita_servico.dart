import 'package:flutter/foundation.dart';
import '../models/receita.dart';
import '../models/ingrediente.dart';
import 'servico_armazenamento.dart';
import 'api_receita_servico.dart';  // Adicionado para integração com backend
import 'conectividade_servico.dart'; // Adicionado para verificar conectividade

// Serviço híbrido de receitas (local + backend)
class HibridoReceitaService {
  static const String _keyReceitas = 'receitas_locais';
  
  // Serviços para integração com backend
  final ApiReceitaService _apiServico = ApiReceitaService();
  final ConectividadeServico _conectividadeServico = ConectividadeServico();

  // ===== OPERAÇÕES BÁSICAS =====
  // Obter todas as receitas
  Future<List<Receita>> obterTodasReceitas() async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Se tem conexão, tentar obter do backend primeiro
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando obter receitas do backend...');
          final receitasBackend = await _apiServico.obterTodasReceitas();
          debugPrint('✅ Obtidas ${receitasBackend.length} receitas do backend');
          
          // Salvar localmente para uso offline
          await _guardarReceitas(receitasBackend);
          
          return receitasBackend;
        } catch (apiError) {
          debugPrint('⚠️ Não foi possível obter receitas do backend: $apiError');
          // Se falhar, continua para carregar do armazenamento local
        }
      }

      // Se não tem conexão ou falhou no backend, carregar do armazenamento local
      final receitas = await ServicoArmazenamento.loadObjectList<Receita>(
        _keyReceitas,
        (map) => Receita.fromMap(map),
      );
      
      // Se não há receitas, criar algumas de exemplo
      if (receitas.isEmpty) {
        return await _criarReceitasExemplo();
      }
      
      return receitas;
    } catch (e) {
      debugPrint('Erro ao obter receitas: $e');
      return await _criarReceitasExemplo();
    }
  }

  // Criar nova receita
  Future<Receita> criarReceita(Receita receita) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Tentar salvar no backend primeiro, se houver conexão
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando salvar receita no backend...');
          final receitaSalva = await _apiServico.criarReceita(receita);
          debugPrint('✅ Receita salva com sucesso no backend!');
          
          // Também guardar localmente a versão do backend
          final receitas = await obterTodasReceitas();
          final index = receitas.indexWhere((r) => r.id == receitaSalva.id);
          if (index >= 0) {
            receitas[index] = receitaSalva;
          } else {
            receitas.add(receitaSalva);
          }
          await _guardarReceitas(receitas);
          
          return receitaSalva;
        } catch (apiError) {
          debugPrint('❌ Erro ao salvar no backend: $apiError');
          // Se falhar no backend, continua para salvar apenas localmente
        }
      }
      
      // Salvar apenas localmente
      final receitas = await obterTodasReceitas();
      
      // Gerar ID único se não existir
      if (receita.id == null || receita.id!.isEmpty) {
        receita = receita.copyWith(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
        );
      }
      
      receitas.add(receita);
      await _guardarReceitas(receitas);
      
      return receita;
    } catch (e) {
      throw Exception('Erro ao criar receita: $e');
    }
  }

  // Atualizar receita existente
  Future<Receita> atualizarReceita(Receita receita) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Se tem conexão e a receita tem ID, tentar atualizar no backend
      if (temConexao && receita.id != null) {
        try {
          debugPrint('🌐 Tentando atualizar receita no backend...');
          final receitaAtualizada = await _apiServico.atualizarReceita(receita);
          debugPrint('✅ Receita atualizada com sucesso no backend!');
          
          // Também atualizar localmente
          final receitas = await obterTodasReceitas();
          final index = receitas.indexWhere((r) => r.id == receita.id);
          if (index >= 0) {
            receitas[index] = receitaAtualizada;
            await _guardarReceitas(receitas);
          }
          
          return receitaAtualizada;
        } catch (apiError) {
          debugPrint('❌ Erro ao atualizar no backend: $apiError');
          // Se falhar no backend, continua para atualizar apenas localmente
        }
      }
      
      // Atualizar apenas localmente
      final receitas = await obterTodasReceitas();
      final index = receitas.indexWhere((r) => r.id == receita.id);
      
      if (index >= 0) {
        receitas[index] = receita;
        await _guardarReceitas(receitas);
        return receita;
      } else {
        throw Exception('Receita não encontrada para atualização');
      }
    } catch (e) {
      throw Exception('Erro ao atualizar receita: $e');
    }
  }

  // Eliminar receita
  Future<bool> eliminarReceita(String id) async {
    try {
      // Verificar se há conexão com o backend
      final bool temConexao = _conectividadeServico.isOnline;
      
      // Tentar eliminar do backend primeiro, se houver conexão
      if (temConexao) {
        try {
          debugPrint('🌐 Tentando eliminar receita do backend...');
          
          // Converter para int se o backend espera um ID numérico
          final int? idNumerico = int.tryParse(id);
          if (idNumerico != null) {
            await _apiServico.eliminarReceita(idNumerico);
            debugPrint('✅ Receita eliminada com sucesso do backend!');
          } else {
            debugPrint('⚠️ ID não numérico: $id');
          }
        } catch (apiError) {
          debugPrint('❌ Erro ao eliminar do backend: $apiError');
          // Se falhar no backend, continua para eliminar apenas localmente
        }
      }
      
      // Eliminar localmente
      final receitas = await obterTodasReceitas();
      final index = receitas.indexWhere((r) => r.id == id);
      
      if (index >= 0) {
        receitas.removeAt(index);
        await _guardarReceitas(receitas);
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Erro ao eliminar receita: $e');
      return false;
    }
  }

  // Pesquisar receitas por título
  Future<List<Receita>> pesquisarReceitas(String termo) async {
    try {
      final receitas = await obterTodasReceitas();
      final termoLower = termo.toLowerCase();

      return receitas.where((receita) {
        return receita.titulo.toLowerCase().contains(termoLower) ||
               receita.descricao.toLowerCase().contains(termoLower);
      }).toList();
    } catch (e) {
      debugPrint('Erro ao pesquisar receitas: $e');
      return [];
    }
  }

  // ===== MÉTODOS AUXILIARES =====

  // Guardar lista de receitas
  Future<void> _guardarReceitas(List<Receita> receitas) async {
    try {
      await ServicoArmazenamento.saveObjectList(
        _keyReceitas,
        receitas,
        (receita) => receita.toMap(),
      );
    } catch (e) {
      throw Exception('Erro ao guardar receitas: $e');
    }
  }

  // Criar receitas de exemplo
  Future<List<Receita>> _criarReceitasExemplo() async {
    final receitasExemplo = [
      Receita(
        id: '1',
        titulo: 'Massa à Bolonhesa',
        descricao: 'Deliciosa massa com molho de carne',
        instrucoes: '1. Cozinhe a massa\n2. Prepare o molho\n3. Misture tudo',
        tempoPreparo: 30,
        numeroPorcoes: 4,
        dificuldade: 'Médio',
        ingredientes: [
          Ingrediente(nome: 'Massa', quantidade: 500, unidade: 'g'),
          Ingrediente(nome: 'Carne picada', quantidade: 300, unidade: 'g'),
          Ingrediente(nome: 'Tomate', quantidade: 2, unidade: 'unidades'),
        ],
      ),
      Receita(
        id: '2',
        titulo: 'Salada Caesar',
        descricao: 'Salada fresca com molho caesar',
        instrucoes: '1. Lave a alface\n2. Prepare o molho\n3. Misture',
        tempoPreparo: 15,
        numeroPorcoes: 2,
        dificuldade: 'Fácil',
        ingredientes: [
          Ingrediente(nome: 'Alface', quantidade: 1, unidade: 'unidade'),
          Ingrediente(nome: 'Queijo parmesão', quantidade: 50, unidade: 'g'),
          Ingrediente(nome: 'Croutons', quantidade: 30, unidade: 'g'),
        ],
      ),
    ];

    await _guardarReceitas(receitasExemplo);
    return receitasExemplo;
  }
}
