# 🐛 Perguntas de Debugging e Troubleshooting - EST Receitas

## 🔍 Problemas Identificados e Soluções

### **Problema: Token JWT não Persistido**
1. **Como identificou que o token não estava sendo salvo?**
   - R: Logs de debug mostravam "Token não disponível", funcionalidades backend falhavam

2. **Qual era a causa raiz do problema?**
   - R: Método copyWith() no modelo Utilizador não incluía o campo token

3. **Como foi debuggado o fluxo de autenticação?**
   - R: Logs detalhados em cada etapa, verificação do SharedPreferences, análise do response da API

4. **Que testes poderiam ter detectado este problema?**
   - R: Unit tests do HibridoUtilizadorServico, integration tests do fluxo de login

5. **Como prevenir problemas similares no futuro?**
   - R: Testes automatizados, validação de campos obrigatórios, logs estruturados

### **Problema: Campos de Password no Perfil**
6. **Por que a funcionalidade de alteração de password não funcionava?**
   - R: Complexidade desnecessária, validação inconsistente, UX confusa

7. **Qual foi a decisão de design tomada?**
   - R: Remover completamente os campos de password, simplificar o formulário

8. **Como foi garantida a integridade após a remoção?**
   - R: Remoção completa de referências, testes manuais, verificação de erros de compilação

9. **Que alternativas foram consideradas?**
   - R: Corrigir a funcionalidade vs. remover, optou-se pela simplicidade

### **Problema: Sincronização Inconsistente**
10. **Como detectar quando a sincronização falha?**
    - R: Logs de erro, timeouts, verificação de conectividade, status codes HTTP

11. **Que estratégias de retry são implementadas?**
    - R: Atualmente básicas, pode ser melhorado com exponential backoff

12. **Como garantir que dados não se percam durante falhas?**
    - R: Persistência local primeiro, queue de operações pendentes

---

## 🔧 Debugging Frontend (Flutter)

### **Problemas de Estado**
13. **Como debuggar problemas de gestão de estado?**
    - R: Flutter Inspector, debugPrint(), breakpoints, widget rebuild tracking

14. **Como identificar memory leaks em Flutter?**
    - R: Observatory, memory profiler, dispose() de controllers, weak references

15. **Como debuggar problemas de navegação?**
    - R: Navigator observer, route logging, stack trace analysis

16. **Como resolver problemas de hot reload?**
    - R: Hot restart, clean build, dependency conflicts, state preservation

### **Problemas de UI**
17. **Como debuggar overflow de widgets?**
    - R: Flutter Inspector, debug flags, Flexible/Expanded widgets

18. **Como resolver problemas de performance em listas?**
    - R: ListView.builder, performance overlay, profiling tools

19. **Como debuggar problemas de responsividade?**
    - R: Device preview, breakpoint debugging, MediaQuery analysis

20. **Como resolver problemas de temas e cores?**
    - R: Theme inspector, color debugging, inheritance chain analysis

### **Problemas de Dados**
21. **Como debuggar problemas de serialização JSON?**
    - R: Try-catch detalhado, JSON validation, schema comparison

22. **Como resolver problemas de SharedPreferences?**
    - R: Clear storage, type checking, async/await verification

23. **Como debuggar problemas de HTTP requests?**
    - R: Network inspector, request/response logging, timeout analysis

24. **Como resolver problemas de async/await?**
    - R: Future debugging, exception handling, race condition analysis

---

## 🖥️ Debugging Backend (Spring Boot)

### **Problemas de Configuração**
25. **Como debuggar problemas de startup do Spring Boot?**
    - R: Application logs, dependency injection errors, configuration validation

26. **Como resolver conflitos de dependências?**
    - R: Dependency tree analysis, version conflicts, exclusions

27. **Como debuggar problemas de base de dados?**
    - R: SQL logging, connection pool monitoring, transaction analysis

28. **Como resolver problemas de CORS?**
    - R: Browser network tab, preflight requests, configuration verification

### **Problemas de Performance**
29. **Como identificar queries lentas?**
    - R: SQL logging, query execution plans, database profiling

30. **Como debuggar problemas de memory leaks?**
    - R: JVM profiling, heap dumps, garbage collection analysis

31. **Como resolver problemas de connection pooling?**
    - R: Pool metrics, connection timeouts, configuration tuning

32. **Como debuggar problemas de threading?**
    - R: Thread dumps, deadlock detection, async processing analysis

### **Problemas de Segurança**
33. **Como debuggar problemas de autenticação JWT?**
    - R: Token validation, signature verification, expiration checking

34. **Como resolver problemas de autorização?**
    - R: Role debugging, permission checking, security context analysis

35. **Como debuggar problemas de HTTPS/TLS?**
    - R: Certificate validation, cipher suites, handshake analysis

---

## 🌐 Debugging Integração

### **Problemas de Comunicação**
36. **Como debuggar timeouts de rede?**
    - R: Network monitoring, latency analysis, retry mechanisms

37. **Como resolver problemas de conectividade intermitente?**
    - R: Connection pooling, circuit breaker patterns, health checks

38. **Como debuggar problemas de serialização entre frontend e backend?**
    - R: Request/response comparison, schema validation, type checking

39. **Como resolver discrepâncias de dados entre local e remoto?**
    - R: Data comparison, sync logging, conflict resolution analysis

### **Problemas de Sincronização**
40. **Como debuggar race conditions em sincronização?**
    - R: Timing analysis, atomic operations, lock debugging

41. **Como resolver problemas de eventual consistency?**
    - R: Timestamp comparison, version vectors, conflict detection

42. **Como debuggar problemas de offline/online transitions?**
    - R: Connectivity monitoring, state machine debugging, queue analysis

---

## 🛠️ Ferramentas de Debugging

### **Frontend Tools**
43. **Que ferramentas usar para debugging Flutter?**
    - R: Flutter Inspector, Dart DevTools, VS Code debugger, Observatory

44. **Como usar o Flutter Performance Overlay?**
    - R: Identificar janky frames, GPU/CPU usage, memory allocation

45. **Como debuggar com breakpoints efetivamente?**
    - R: Conditional breakpoints, watch expressions, call stack analysis

46. **Que ferramentas usar para network debugging?**
    - R: Charles Proxy, Wireshark, browser dev tools, Postman

### **Backend Tools**
47. **Que ferramentas usar para debugging Spring Boot?**
    - R: IntelliJ debugger, Spring Boot Actuator, JProfiler, VisualVM

48. **Como usar JVM profiling tools?**
    - R: Memory analysis, CPU profiling, thread analysis, GC monitoring

49. **Que ferramentas usar para database debugging?**
    - R: SQL profilers, query analyzers, database monitoring tools

50. **Como usar APM tools para monitoring?**
    - R: New Relic, DataDog, AppDynamics para performance monitoring

---

## 📊 Logging e Monitorização

### **Estratégias de Logging**
51. **Como implementar logging estruturado?**
    - R: JSON format, correlation IDs, log levels apropriados

52. **Que informações incluir nos logs de debug?**
    - R: Request IDs, user context, timing information, error details

53. **Como implementar distributed tracing?**
    - R: OpenTracing, Jaeger, correlation across services

54. **Como balancear verbosidade vs. performance nos logs?**
    - R: Log levels dinâmicos, sampling, async logging

### **Métricas e Alertas**
55. **Que métricas são críticas para monitorizar?**
    - R: Response time, error rate, throughput, resource utilization

56. **Como implementar alertas efetivos?**
    - R: Threshold-based, anomaly detection, escalation policies

57. **Como fazer root cause analysis efetiva?**
    - R: Correlation analysis, timeline reconstruction, dependency mapping

---

## 🧪 Testing para Debugging

### **Estratégias de Teste**
58. **Como usar testes para debugging?**
    - R: Reproduzir bugs, regression testing, edge case coverage

59. **Como implementar testes de integração efetivos?**
    - R: End-to-end scenarios, data setup/teardown, environment isolation

60. **Como usar property-based testing?**
    - R: Generate test cases, edge case discovery, invariant checking

61. **Como implementar chaos engineering?**
    - R: Failure injection, resilience testing, recovery validation

### **Test Automation**
62. **Como automatizar debugging de regressões?**
    - R: Automated test suites, CI/CD integration, failure analysis

63. **Como usar mutation testing?**
    - R: Test quality assessment, coverage gaps identification

64. **Como implementar contract testing?**
    - R: API contract validation, consumer-driven contracts

---

## 🚨 Troubleshooting em Produção

### **Problemas de Produção**
65. **Como debuggar problemas apenas em produção?**
    - R: Remote debugging, production logs, feature flags

66. **Como fazer rollback seguro?**
    - R: Blue-green deployment, database migrations, data consistency

67. **Como debuggar problemas de performance em produção?**
    - R: APM tools, profiling in production, load testing

68. **Como lidar com data corruption?**
    - R: Backup restoration, data validation, consistency checks

### **Incident Response**
69. **Como estruturar incident response?**
    - R: Runbooks, escalation procedures, communication protocols

70. **Como fazer post-mortem efetivo?**
    - R: Timeline reconstruction, root cause analysis, action items

71. **Como implementar circuit breakers para falhas?**
    - R: Failure detection, automatic recovery, manual override

---

## 🔄 Debugging Específico do Projeto

### **Problemas Conhecidos EST Receitas**
72. **Como debuggar problemas de autenticação híbrida?**
    - R: Verificar token persistence, fallback logic, sync status

73. **Como resolver problemas de sincronização de receitas?**
    - R: Check network connectivity, validate data format, conflict resolution

74. **Como debuggar problemas de SharedPreferences?**
    - R: Clear app data, check serialization, validate JSON format

75. **Como resolver problemas de navegação entre telas?**
    - R: Route debugging, state preservation, context validation

### **Cenários de Falha Específicos**
76. **O que fazer quando o backend está inacessível?**
    - R: Fallback para dados locais, queue operations, user notification

77. **Como lidar com dados corrompidos no cache local?**
    - R: Data validation, cache invalidation, fresh data fetch

78. **Como debuggar problemas de formulários de receitas?**
    - R: Validation logic, field state, submission flow

79. **Como resolver problemas de listagem de receitas?**
    - R: Data loading, pagination, filtering logic

80. **Como debuggar problemas de gestão de perfil?**
    - R: Form validation, data persistence, update synchronization

---

*Este guia de debugging fornece estratégias práticas para identificar, diagnosticar e resolver problemas comuns no desenvolvimento e manutenção do projeto EST Receitas.*
