#!/usr/bin/env python3
"""
Ser<PERSON><PERSON> para simular o backend Spring Boot
Usado quando Maven não está disponível
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime
import uuid

# Dados em memória para simular a base de dados
receitas_db = []
stock_db = []

class MockAPIHandler(BaseHTTPRequestHandler):
    
    def _set_cors_headers(self):
        """Configurar headers CORS"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def _send_json_response(self, data, status_code=200):
        """Enviar resposta JSON"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self._set_cors_headers()
        self.end_headers()
        
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def _send_error_response(self, message, status_code=400):
        """Enviar resposta de erro"""
        error_data = {
            'error': message,
            'timestamp': datetime.now().isoformat(),
            'status': status_code
        }
        self._send_json_response(error_data, status_code)
    
    def _read_request_body(self):
        """Ler corpo da requisição"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                body = self.rfile.read(content_length)
                return json.loads(body.decode('utf-8'))
            return {}
        except Exception as e:
            print(f"Erro ao ler corpo da requisição: {e}")
            return {}
    
    def do_OPTIONS(self):
        """Responder a requisições OPTIONS (CORS preflight)"""
        self.send_response(200)
        self._set_cors_headers()
        self.end_headers()
    
    def do_GET(self):
        """Processar requisições GET"""
        path = urllib.parse.urlparse(self.path).path
        print(f"GET {path}")
        
        if path == '/api/test/health':
            self._send_json_response({
                'status': 'OK',
                'message': 'Mock API está funcionando',
                'timestamp': datetime.now().isoformat()
            })
        
        elif path == '/api/receitas':
            self._send_json_response(receitas_db)
        
        elif path.startswith('/api/receitas/'):
            receita_id = path.split('/')[-1]
            receita = next((r for r in receitas_db if str(r['id']) == receita_id), None)
            if receita:
                self._send_json_response(receita)
            else:
                self._send_error_response('Receita não encontrada', 404)
        
        elif path == '/api/stock':
            self._send_json_response(stock_db)
        
        elif path.startswith('/api/stock/'):
            item_id = path.split('/')[-1]
            item = next((i for i in stock_db if str(i['id']) == item_id), None)
            if item:
                self._send_json_response(item)
            else:
                self._send_error_response('Item não encontrado', 404)
        
        else:
            self._send_error_response('Endpoint não encontrado', 404)
    
    def do_POST(self):
        """Processar requisições POST"""
        path = urllib.parse.urlparse(self.path).path
        data = self._read_request_body()
        print(f"POST {path} - Data: {data}")
        
        if path == '/api/receitas':
            # Criar nova receita
            receita = {
                'id': str(uuid.uuid4()),
                'titulo': data.get('titulo', ''),
                'descricao': data.get('descricao', ''),
                'instrucoes': data.get('instrucoes', ''),
                'tempoPreparo': data.get('tempoPreparo', 0),
                'numeroPorcoes': data.get('numeroPorcoes', 1),
                'dificuldade': data.get('dificuldade', 'Fácil'),
                'categoria': data.get('categoria', 'Geral'),
                'imagemUrl': data.get('imagemUrl'),
                'ingredientes': data.get('ingredientes', []),
                'dataCriacao': datetime.now().isoformat()
            }
            receitas_db.append(receita)
            self._send_json_response(receita, 201)
        
        elif path == '/api/stock':
            # Criar novo item de stock
            item = {
                'id': len(stock_db) + 1,
                'nome': data.get('nome', ''),
                'quantidade': data.get('quantidade', 0),
                'unidade': data.get('unidade', ''),
                'localizacao': data.get('localizacao', 'DESPENSA'),
                'dataValidade': data.get('dataValidade'),
                'observacoes': data.get('observacoes'),
                'dataCriacao': datetime.now().isoformat()
            }
            stock_db.append(item)
            self._send_json_response(item, 201)
        
        else:
            self._send_error_response('Endpoint não encontrado', 404)
    
    def do_PUT(self):
        """Processar requisições PUT"""
        path = urllib.parse.urlparse(self.path).path
        data = self._read_request_body()
        print(f"PUT {path} - Data: {data}")
        
        if path.startswith('/api/receitas/'):
            receita_id = path.split('/')[-1]
            receita_index = next((i for i, r in enumerate(receitas_db) if str(r['id']) == receita_id), None)
            if receita_index is not None:
                receitas_db[receita_index].update(data)
                self._send_json_response(receitas_db[receita_index])
            else:
                self._send_error_response('Receita não encontrada', 404)
        
        elif path.startswith('/api/stock/'):
            item_id = path.split('/')[-1]
            item_index = next((i for i, item in enumerate(stock_db) if str(item['id']) == item_id), None)
            if item_index is not None:
                stock_db[item_index].update(data)
                self._send_json_response(stock_db[item_index])
            else:
                self._send_error_response('Item não encontrado', 404)
        
        else:
            self._send_error_response('Endpoint não encontrado', 404)
    
    def do_DELETE(self):
        """Processar requisições DELETE"""
        path = urllib.parse.urlparse(self.path).path
        print(f"DELETE {path}")
        
        if path.startswith('/api/receitas/'):
            receita_id = path.split('/')[-1]
            receita_index = next((i for i, r in enumerate(receitas_db) if str(r['id']) == receita_id), None)
            if receita_index is not None:
                del receitas_db[receita_index]
                self._send_json_response({'message': 'Receita eliminada com sucesso'})
            else:
                self._send_error_response('Receita não encontrada', 404)
        
        elif path.startswith('/api/stock/'):
            item_id = path.split('/')[-1]
            item_index = next((i for i, item in enumerate(stock_db) if str(item['id']) == item_id), None)
            if item_index is not None:
                del stock_db[item_index]
                self._send_json_response({'message': 'Item eliminado com sucesso'})
            else:
                self._send_error_response('Item não encontrado', 404)
        
        else:
            self._send_error_response('Endpoint não encontrado', 404)
    
    def log_message(self, format, *args):
        """Personalizar logs"""
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {format % args}")

def run_mock_server(port=8080):
    """Executar servidor mock"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MockAPIHandler)
    
    print(f"🚀 Servidor Mock iniciado em http://localhost:{port}")
    print(f"📡 Endpoints disponíveis:")
    print(f"   GET  /api/test/health")
    print(f"   GET  /api/receitas")
    print(f"   POST /api/receitas")
    print(f"   GET  /api/stock")
    print(f"   POST /api/stock")
    print(f"🛑 Pressione Ctrl+C para parar")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n🛑 Servidor Mock parado")
        httpd.server_close()

if __name__ == '__main__':
    # Adicionar alguns dados de exemplo
    receitas_db.extend([
        {
            'id': '1',
            'titulo': 'Massa à Bolonhesa',
            'descricao': 'Deliciosa massa com molho bolonhesa',
            'instrucoes': 'Cozinhar a massa, preparar o molho, misturar.',
            'tempoPreparo': 30,
            'numeroPorcoes': 4,
            'dificuldade': 'Médio',
            'categoria': 'Pratos Principais',
            'imagemUrl': None,
            'ingredientes': [
                {'nome': 'Massa', 'quantidade': 400, 'unidade': 'g'},
                {'nome': 'Carne Picada', 'quantidade': 300, 'unidade': 'g'},
                {'nome': 'Tomate', 'quantidade': 2, 'unidade': 'unidades'}
            ],
            'dataCriacao': datetime.now().isoformat()
        }
    ])
    
    stock_db.extend([
        {
            'id': 1,
            'nome': 'Arroz',
            'quantidade': 2.0,
            'unidade': 'kg',
            'localizacao': 'DESPENSA',
            'dataValidade': '2024-12-31',
            'observacoes': None,
            'dataCriacao': datetime.now().isoformat()
        }
    ])
    
    run_mock_server()
