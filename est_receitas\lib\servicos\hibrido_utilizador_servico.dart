import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/utilizador.dart';
import 'servico_armazenamento.dart';
import 'api_utilizador_servico.dart';

/// Classe para representar o resultado de operações de autenticação
/// Encapsula o sucesso/falha, mensagem e dados do utilizador
class ResultadoAutenticacao {
  final bool sucesso;
  final String mensagem;
  final Utilizador? utilizador;

  const ResultadoAutenticacao({
    required this.sucesso,
    required this.mensagem,
    this.utilizador,
  });

  /// Factory para criar resultado de sucesso
  factory ResultadoAutenticacao.sucesso(Utilizador utilizador, [String mensagem = 'Operação realizada com sucesso']) {
    return ResultadoAutenticacao(
      sucesso: true,
      mensagem: mensagem,
      utilizador: utilizador,
    );
  }

  /// Factory para criar resultado de erro
  factory ResultadoAutenticacao.erro(String mensagem) {
    return ResultadoAutenticacao(
      sucesso: false,
      mensagem: mensagem,
    );
  }
}

/// Serviço híbrido de utilizadores (armazenamento local + API backend)
/// Gere autenticação, registo e perfis de utilizadores
/// Funciona offline com sincronização quando há conectividade
class HibridoUtilizadorServico {
  // === CONSTANTES ===
  static const String _keyUtilizadores = 'utilizadores_locais';
  static const String _keyUtilizadorAtual = 'utilizador_atual';

  // === PROPRIEDADES PRIVADAS ===
  Utilizador? _utilizadorAtual;
  final _apiServico = ApiUtilizadorServico(); // Serviço para comunicação com API

  /// Stream controller para notificar mudanças no estado do utilizador
  final _utilizadorController = StreamController<Utilizador?>.broadcast();

  // === PROPRIEDADES PÚBLICAS ===

  /// Stream para escutar mudanças no utilizador atual
  Stream<Utilizador?> get utilizadorStream => _utilizadorController.stream;
  
  // Utilizador atual
  Utilizador? get utilizadorAtual => _utilizadorAtual;
  
  // Verificar se está autenticado
  bool get isAutenticado => _utilizadorAtual != null;

  // Inicializar o serviço
  Future<void> inicializar() async {
    try {
      // Carregar utilizador atual do armazenamento
      _utilizadorAtual = await ServicoArmazenamento.loadObject<Utilizador>(
        _keyUtilizadorAtual,
        (map) => Utilizador.fromMap(map),
      );
      if (_utilizadorAtual != null) {
        _utilizadorController.add(_utilizadorAtual);
      }
    } catch (e) {
      debugPrint('Erro ao inicializar serviço de utilizadores: $e');
    }
  }

  // ===== AUTENTICAÇÃO =====

  // Registar novo utilizador
  Future<ResultadoAutenticacao> registar({
    required String nome,
    required String email,
    required String password,
    String? telefone,
  }) async {
    try {
      // Verificar email duplicado
      final utilizadores = await _obterTodosUtilizadores();
      final emailExiste = utilizadores.any((u) => u.email.toLowerCase() == email.toLowerCase());
      
      if (emailExiste) {
        return ResultadoAutenticacao.erro('Já existe um utilizador com este email');
      }

      // Criar novo utilizador
      final novoUtilizador = Utilizador(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        nome: nome,
        email: email,
        password: password, // Em produção, deveria ser hash
        telefone: telefone,
      );

      // Guardar localmente
      utilizadores.add(novoUtilizador);
      await _guardarUtilizadores(utilizadores);

      // Tentar registrar no backend também
      try {
        final authResponse = await _apiServico.registarUtilizador(
          nome: nome,
          email: email,
          password: password,
          confirmPassword: password,
        );

        if (authResponse.success && authResponse.token != null) {
          // Se o registo retornou um token, atualizar o utilizador com o token
          final utilizadorComToken = novoUtilizador.copyWith(
            token: authResponse.token,
            id: authResponse.userId?.toString() ?? novoUtilizador.id,
          );

          // Atualizar na lista e como utilizador atual
          utilizadores[utilizadores.length - 1] = utilizadorComToken;
          await _guardarUtilizadores(utilizadores);
          await _definirUtilizadorAtual(utilizadorComToken);

          debugPrint('✅ Utilizador registrado no backend com token');
          return ResultadoAutenticacao.sucesso(utilizadorComToken, 'Utilizador registado com sucesso');
        }

        debugPrint('✅ Utilizador registrado no backend com sucesso');
      } catch (e) {
        debugPrint('⚠️ Erro ao registrar no backend: $e');
        // Continuamos mesmo se falhar no backend, o usuário já está salvo localmente
      }

      // Login automático
      await _definirUtilizadorAtual(novoUtilizador);

      return ResultadoAutenticacao.sucesso(novoUtilizador, 'Utilizador registado com sucesso');
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro no registo: $e');
    }
  }

  // Fazer login
  Future<ResultadoAutenticacao> login({
    required String email,
    required String password,
  }) async {
    try {
      // Primeiro tentar login no backend
      try {
        final authResponse = await _apiServico.login(
          email: email,
          password: password,
        );
        
        if (authResponse.success) {
          // Se login na API foi bem sucedido, criar/atualizar usuário local
          final utilizador = Utilizador(
            id: authResponse.userId?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
            nome: authResponse.nome ?? email.split('@').first,
            email: email,
            password: password, // Armazenamento local simplificado
            token: authResponse.token, // Salvar o token para futuras operações
          );

          // Definir como utilizador atual e salvar localmente
          await _definirUtilizadorAtual(utilizador);

          debugPrint('✅ Login com token salvo: ${authResponse.token?.substring(0, 20)}...');
          return ResultadoAutenticacao.sucesso(utilizador, 'Login realizado com sucesso');
        }
      } catch (e) {
        debugPrint('⚠️ Falha no login na API, tentando local: $e');
      }
      
      // Se falhar no backend, tentar login local
      final utilizadores = await _obterTodosUtilizadores();
      
      // Procurar utilizador
      final utilizador = utilizadores.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase() && u.password == password,
        orElse: () => throw Exception('Credenciais inválidas'),
      );

      // Definir como utilizador atual
      await _definirUtilizadorAtual(utilizador);

      return ResultadoAutenticacao.sucesso(utilizador, 'Login realizado com sucesso');
    } catch (e) {
      return ResultadoAutenticacao.erro('Email ou password incorretos');
    }
  }

  // Fazer logout
  Future<void> logout() async {
    try {
      _utilizadorAtual = null;
      await ServicoArmazenamento.delete(_keyUtilizadorAtual);
      _utilizadorController.add(null);
    } catch (e) {
      debugPrint('Erro no logout: $e');
    }
  }

  // ===== GESTÃO DE PERFIL =====

  // Atualizar perfil
  Future<ResultadoAutenticacao> atualizarPerfil(Utilizador utilizadorAtualizado) async {
    try {
      if (_utilizadorAtual == null) {
        return ResultadoAutenticacao.erro('Nenhum utilizador autenticado');
      }
      
      // Tentar primeiro atualizar no backend (se tiver token de autenticação)
      bool atualizadoNoBackend = false;
      
      // Token seria armazenado após login bem-sucedido no backend
      final String? token = _utilizadorAtual!.token;
      
      if (token != null && token.isNotEmpty) {
        try {
          debugPrint('🌐 Tentando atualizar perfil no backend...');
          await _apiServico.atualizarPerfil(
            token: token,
            nome: utilizadorAtualizado.nome,
            email: utilizadorAtualizado.email,
          );
          atualizadoNoBackend = true;
          debugPrint('✅ Perfil atualizado com sucesso no backend!');
        } catch (e) {
          debugPrint('❌ Erro ao atualizar perfil no backend: $e');
          // Se falhar no backend, continua para atualizar apenas localmente
        }
      } else {
        debugPrint('⚠️ Token não disponível, atualizando apenas localmente');
      }

      // Atualizar na lista de utilizadores local
      final utilizadores = await _obterTodosUtilizadores();


      // Tentar encontrar por ID primeiro
      int index = utilizadores.indexWhere((u) => u.id == _utilizadorAtual!.id);

      // Fallback: procurar por email
      if (index < 0) {
        debugPrint('⚠️ Utilizador não encontrado por ID, tentando por email...');
        index = utilizadores.indexWhere((u) => u.email == _utilizadorAtual!.email);
      }

      if (index >= 0) {
        debugPrint('✅ Utilizador encontrado no índice: $index');
        utilizadores[index] = utilizadorAtualizado;
        await _guardarUtilizadores(utilizadores);
        await _definirUtilizadorAtual(utilizadorAtualizado);

        final mensagem = atualizadoNoBackend
            ? 'Perfil atualizado com sucesso (local e backend)'
            : 'Perfil atualizado com sucesso (apenas local)';

        return ResultadoAutenticacao.sucesso(utilizadorAtualizado, mensagem);
      }

      // Adicionar como novo utilizador
      debugPrint('⚠️ Utilizador não encontrado na lista, adicionando como novo...');
      utilizadores.add(utilizadorAtualizado);
      await _guardarUtilizadores(utilizadores);
      await _definirUtilizadorAtual(utilizadorAtualizado);

      final mensagem = atualizadoNoBackend
          ? 'Perfil criado/atualizado com sucesso (local e backend)'
          : 'Perfil criado/atualizado com sucesso (apenas local)';

      return ResultadoAutenticacao.sucesso(utilizadorAtualizado, mensagem);
    } catch (e) {
      return ResultadoAutenticacao.erro('Erro ao atualizar perfil: $e');
    }
  }

  // Alterar password
  Future<ResultadoAutenticacao> alterarPassword({
    required String passwordAtual,
    required String novaPassword,
  }) async {
    try {
      if (_utilizadorAtual == null) {
        debugPrint('❌ Nenhum utilizador autenticado para alterar password');
        return ResultadoAutenticacao.erro('Nenhum utilizador autenticado');
      }



      if (_utilizadorAtual!.password != passwordAtual) {
        debugPrint('❌ Password atual incorreta');
        return ResultadoAutenticacao.erro('Password atual incorreta');
      }



      final utilizadorAtualizado = _utilizadorAtual!.copyWith(password: novaPassword);


      final resultado = await atualizarPerfil(utilizadorAtualizado);

      if (resultado.sucesso) {

      } else {
        debugPrint('❌ Erro ao atualizar perfil com nova password: ${resultado.mensagem}');
      }

      return resultado;
    } catch (e) {
      debugPrint('❌ Erro ao alterar password: $e');
      return ResultadoAutenticacao.erro('Erro ao alterar password: $e');
    }
  }

  // Obter todos os utilizadores
  Future<List<Utilizador>> obterTodosUtilizadores() async {
    return await _obterTodosUtilizadores();
  }

  // Verificar se é o primeiro utilizador
  Future<bool> isPrimeiroUtilizador() async {
    try {
      final utilizadores = await _obterTodosUtilizadores();
      return utilizadores.isEmpty;
    } catch (e) {
      return true;
    }
  }

  // Limpar todos os dados
  Future<void> limparTodosDados() async {
    try {
      await ServicoArmazenamento.delete(_keyUtilizadores);
      await ServicoArmazenamento.delete(_keyUtilizadorAtual);
      _utilizadorAtual = null;
      _utilizadorController.add(null);
    } catch (e) {
      debugPrint('Erro ao limpar dados: $e');
    }
  }

  // ===== MÉTODOS AUXILIARES =====

  // Obter todos os utilizadores do armazenamento
  Future<List<Utilizador>> _obterTodosUtilizadores() async {
    try {
      return await ServicoArmazenamento.loadObjectList<Utilizador>(
        _keyUtilizadores,
        (map) => Utilizador.fromMap(map),
      );
    } catch (e) {
      return [];
    }
  }

  // Guardar lista de utilizadores
  Future<void> _guardarUtilizadores(List<Utilizador> utilizadores) async {
    await ServicoArmazenamento.saveObjectList(
      _keyUtilizadores,
      utilizadores,
      (utilizador) => utilizador.toMap(),
    );
  }

  // Definir utilizador atual
  Future<void> _definirUtilizadorAtual(Utilizador utilizador) async {
    _utilizadorAtual = utilizador;
    await ServicoArmazenamento.saveObject(_keyUtilizadorAtual, utilizador, (u) => u.toMap());
    _utilizadorController.add(utilizador);
  }

  // Dispose
  void dispose() {
    _utilizadorController.close();
  }
}

// Aliases para compatibilidade
typedef HybridUserService = HibridoUtilizadorServico;
