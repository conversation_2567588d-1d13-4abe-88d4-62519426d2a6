# ===================================================================
# CONFIGURAÇÃO DA APLICAÇÃO EST RECEITAS BACKEND
# ===================================================================
# Este ficheiro contém todas as configurações da aplicação Spring Boot
# para desenvolvimento. Para produção, usar application-prod.properties
# ===================================================================

# === CONFIGURAÇÃO DO SERVIDOR ===
# Porta onde o servidor Spring Boot irá executar
server.port=8080

# === CONFIGURAÇÃO DA BASE DE DADOS H2 (DESENVOLVIMENTO) ===
# Base de dados em memória para desenvolvimento e testes
# Os dados são perdidos quando a aplicação é reiniciada
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# Console H2 para visualizar e gerir a base de dados
# Acesso: http://localhost:8080/h2-console
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# === CONFIGURAÇÃO JPA/HIBERNATE ===
# Dialeto específico para H2
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Estratégia de criação de tabelas (create-drop recria a cada reinício)
spring.jpa.hibernate.ddl-auto=create-drop

# Mostrar SQL gerado pelo Hibernate (útil para debug)
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# === CONFIGURAÇÃO DE INICIALIZAÇÃO DE DADOS ===
# Desabilitada para evitar conflitos com dados de teste
spring.jpa.defer-datasource-initialization=false
spring.sql.init.mode=never

# === CONFIGURAÇÃO CORS PARA INTEGRAÇÃO COM FLUTTER ===
# CORS é configurado programaticamente na classe SecurityConfig
# As propriedades spring.web.cors.* não são suportadas no Spring Boot 3.x

# === CONFIGURAÇÃO DE SEGURANÇA BÁSICA ===
# Utilizador admin padrão (apenas para desenvolvimento)
spring.security.user.name=admin
spring.security.user.password=admin
spring.security.user.roles=ADMIN

# === CONFIGURAÇÃO DE LOGGING ===
# Níveis de log para debug durante desenvolvimento
logging.level.com.estrecitas=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# === CONFIGURAÇÃO DE FERRAMENTAS DE DESENVOLVIMENTO ===
# Hot reload e live reload para desenvolvimento
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
