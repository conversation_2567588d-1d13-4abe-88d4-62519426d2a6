package com.estrecitas.repository;

import com.estrecitas.model.Ingrediente;
import com.estrecitas.model.Receita;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IngredienteRepository extends JpaRepository<Ingrediente, Long> {
    
    // Procurar ingredientes por receita
    List<Ingrediente> findByReceita(Receita receita);
    
    // Procurar ingredientes por ID da receita
    List<Ingrediente> findByReceitaId(Long receitaId);
    
    // Procurar ingredientes por nome (case insensitive)
    List<Ingrediente> findByNomeContainingIgnoreCase(String nome);
    
    // Procurar ingredientes por unidade
    List<Ingrediente> findByUnidadeIgnoreCase(String unidade);
    
    // Procurar ingredientes por quantidade mínima
    List<Ingrediente> findByQuantidadeGreaterThanEqual(Double quantidadeMinima);
    
    // Procurar ingredientes por quantidade máxima
    List<Ingrediente> findByQuantidadeLessThanEqual(Double quantidadeMaxima);
    
    // Procurar ingredientes por faixa de quantidade
    List<Ingrediente> findByQuantidadeBetween(Double quantidadeMin, Double quantidadeMax);
    
    // Procurar todos os nomes de ingredientes distintos
    @Query("SELECT DISTINCT i.nome FROM Ingrediente i ORDER BY i.nome")
    List<String> findDistinctNomes();
    
    // Procurar todas as unidades distintas
    @Query("SELECT DISTINCT i.unidade FROM Ingrediente i WHERE i.unidade IS NOT NULL ORDER BY i.unidade")
    List<String> findDistinctUnidades();
    
    // Contar ingredientes por receita
    @Query("SELECT i.receita.id, COUNT(i) FROM Ingrediente i GROUP BY i.receita.id")
    List<Object[]> countIngredientesPorReceita();
    
    // Procurar ingredientes mais utilizados
    @Query("SELECT i.nome, COUNT(i) as uso FROM Ingrediente i GROUP BY i.nome ORDER BY uso DESC")
    List<Object[]> findIngredientesMaisUtilizados();
    
    // Procurar ingredientes por múltiplos critérios
    @Query("SELECT i FROM Ingrediente i WHERE " +
           "(:nome IS NULL OR LOWER(i.nome) LIKE LOWER(CONCAT('%', :nome, '%'))) AND " +
           "(:unidade IS NULL OR LOWER(i.unidade) = LOWER(:unidade)) AND " +
           "(:quantidadeMin IS NULL OR i.quantidade >= :quantidadeMin) AND " +
           "(:quantidadeMax IS NULL OR i.quantidade <= :quantidadeMax)")
    List<Ingrediente> findByMultiplosCriterios(
        @Param("nome") String nome,
        @Param("unidade") String unidade,
        @Param("quantidadeMin") Double quantidadeMin,
        @Param("quantidadeMax") Double quantidadeMax
    );
    
    // Verificar se existe ingrediente com nome específico numa receita
    boolean existsByNomeIgnoreCaseAndReceita(String nome, Receita receita);
    
    // Procurar ingredientes que não estão em nenhuma receita (órfãos)
    @Query("SELECT i FROM Ingrediente i WHERE i.receita IS NULL")
    List<Ingrediente> findIngredientesOrfaos();
}
