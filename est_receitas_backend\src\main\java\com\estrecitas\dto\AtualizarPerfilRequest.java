package com.estrecitas.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Pedido de atualização de perfil de utilizador.
 * Contém os dados necessários para modificar as informações pessoais de um utilizador existente.
 */
public class AtualizarPerfilRequest {
    
    @NotBlank(message = "Nome é obrigatório")
    @Size(min = 2, max = 100, message = "Nome deve ter entre 2 e 100 caracteres")
    private String nome;

    @NotBlank(message = "Email é obrigatório")
    @Email(message = "Email deve ter formato válido")
    @Size(max = 150, message = "Email deve ter no máximo 150 caracteres")
    private String email;
    
    /**
     * Construtor padrão
     */
    public AtualizarPerfilRequest() {}
    
    /**
     * Construtor com parâmetros para criação de um pedido de atualização.
     * 
     * @param nome Nome do utilizador
     * @param email Email do utilizador
     */
    public AtualizarPerfilRequest(String nome, String email) {
        this.nome = nome;
        this.email = email;
    }
    
    // Getters e Setters
    /**
     * Obtém o nome do utilizador.
     * 
     * @return Nome do utilizador
     */
    public String getNome() {
        return nome;
    }
    
    /**
     * Define o nome do utilizador.
     * 
     * @param nome Novo nome a ser definido
     */
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    /**
     * Obtém o email do utilizador.
     * 
     * @return Email do utilizador
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * Define o email do utilizador.
     * 
     * @param email Novo email a ser definido
     */
    public void setEmail(String email) {
        this.email = email;
    }
    
    @Override
    public String toString() {
        return "AtualizarPerfilRequest{" +
                "nome='" + nome + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
