package com.estrecitas.mapper;

import com.estrecitas.dto.IngredienteDTO;
import com.estrecitas.dto.ReceitaDTO;
import com.estrecitas.dto.StockItemDTO;
import com.estrecitas.model.Ingrediente;
import com.estrecitas.model.Receita;
import com.estrecitas.model.StockItem;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DTOMapper {
    
    // ===== RECEITA MAPPING =====
    
    public ReceitaDTO toReceitaDTO(Receita receita) {
        if (receita == null) return null;
        
        ReceitaDTO dto = new ReceitaDTO();
        dto.setId(receita.getId());
        dto.setTitulo(receita.getTitulo());
        dto.setDescricao(receita.getDescricao());
        dto.setInstrucoes(receita.getInstrucoes());
        dto.setTempoPreparo(receita.getTempoPreparo());
        dto.setNumeroPorcoes(receita.getNumeroPorcoes());
        dto.setDificuldade(receita.getDificuldade());
        dto.setImagemUrl(receita.getImagemUrl());
        
        // Mapear ingredientes
        if (receita.getIngredientes() != null) {
            List<IngredienteDTO> ingredientesDTO = receita.getIngredientes().stream()
                    .map(this::toIngredienteDTO)
                    .collect(Collectors.toList());
            dto.setIngredientes(ingredientesDTO);
        }
        
        return dto;
    }
    
    public Receita toReceita(ReceitaDTO dto) {
        if (dto == null) return null;
        
        Receita receita = new Receita();
        receita.setId(dto.getId());
        receita.setTitulo(dto.getTitulo());
        receita.setDescricao(dto.getDescricao());
        receita.setInstrucoes(dto.getInstrucoes());
        receita.setTempoPreparo(dto.getTempoPreparo());
        receita.setNumeroPorcoes(dto.getNumeroPorcoes());
        receita.setDificuldade(dto.getDificuldade());
        receita.setImagemUrl(dto.getImagemUrl());
        
        return receita;
    }
    
    public List<ReceitaDTO> toReceitaDTOList(List<Receita> receitas) {
        if (receitas == null) return null;
        return receitas.stream()
                .map(this::toReceitaDTO)
                .collect(Collectors.toList());
    }
    
    // ===== INGREDIENTE MAPPING =====
    
    public IngredienteDTO toIngredienteDTO(Ingrediente ingrediente) {
        if (ingrediente == null) return null;
        
        IngredienteDTO dto = new IngredienteDTO();
        dto.setId(ingrediente.getId());
        dto.setNome(ingrediente.getNome());
        dto.setQuantidade(ingrediente.getQuantidade());
        dto.setUnidade(ingrediente.getUnidade());
        dto.setObservacoes(ingrediente.getObservacoes());
        
        if (ingrediente.getReceita() != null) {
            dto.setReceitaId(ingrediente.getReceita().getId());
        }
        
        return dto;
    }
    
    public Ingrediente toIngrediente(IngredienteDTO dto) {
        if (dto == null) return null;
        
        Ingrediente ingrediente = new Ingrediente();
        ingrediente.setId(dto.getId());
        ingrediente.setNome(dto.getNome());
        ingrediente.setQuantidade(dto.getQuantidade());
        ingrediente.setUnidade(dto.getUnidade());
        ingrediente.setObservacoes(dto.getObservacoes());
        
        return ingrediente;
    }
    
    public List<IngredienteDTO> toIngredienteDTOList(List<Ingrediente> ingredientes) {
        if (ingredientes == null) return null;
        return ingredientes.stream()
                .map(this::toIngredienteDTO)
                .collect(Collectors.toList());
    }
    
    // ===== STOCK ITEM MAPPING =====
    
    public StockItemDTO toStockItemDTO(StockItem stockItem) {
        if (stockItem == null) return null;
        
        StockItemDTO dto = new StockItemDTO();
        dto.setId(stockItem.getId());
        dto.setNome(stockItem.getNome());
        dto.setQuantidade(stockItem.getQuantidade());
        dto.setUnidade(stockItem.getUnidade());
        dto.setDataValidade(stockItem.getDataValidade());
        dto.setLocalizacao(stockItem.getLocalizacao());
        dto.setObservacoes(stockItem.getObservacoes());
        
        return dto;
    }
    
    public StockItem toStockItem(StockItemDTO dto) {
        if (dto == null) return null;
        
        StockItem stockItem = new StockItem();
        stockItem.setId(dto.getId());
        stockItem.setNome(dto.getNome());
        stockItem.setQuantidade(dto.getQuantidade());
        stockItem.setUnidade(dto.getUnidade());
        stockItem.setDataValidade(dto.getDataValidade());
        stockItem.setLocalizacao(dto.getLocalizacao());
        stockItem.setObservacoes(dto.getObservacoes());
        
        return stockItem;
    }
    
    public List<StockItemDTO> toStockItemDTOList(List<StockItem> stockItems) {
        if (stockItems == null) return null;
        return stockItems.stream()
                .map(this::toStockItemDTO)
                .collect(Collectors.toList());
    }
    
    // ===== MÉTODOS AUXILIARES =====
    
    public void updateReceitaFromDTO(Receita receita, ReceitaDTO dto) {
        if (receita == null || dto == null) return;
        
        receita.setTitulo(dto.getTitulo());
        receita.setDescricao(dto.getDescricao());
        receita.setInstrucoes(dto.getInstrucoes());
        receita.setTempoPreparo(dto.getTempoPreparo());
        receita.setNumeroPorcoes(dto.getNumeroPorcoes());
        receita.setDificuldade(dto.getDificuldade());
        receita.setImagemUrl(dto.getImagemUrl());
    }
    
    public void updateStockItemFromDTO(StockItem stockItem, StockItemDTO dto) {
        if (stockItem == null || dto == null) return;
        
        stockItem.setNome(dto.getNome());
        stockItem.setQuantidade(dto.getQuantidade());
        stockItem.setUnidade(dto.getUnidade());
        stockItem.setDataValidade(dto.getDataValidade());
        stockItem.setLocalizacao(dto.getLocalizacao());
        stockItem.setObservacoes(dto.getObservacoes());
    }
}
