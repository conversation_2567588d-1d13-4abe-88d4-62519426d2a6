package com.estrecitas.dto;

public class IngredienteDTO {
    
    private Long id;
    private String nome;
    private Double quantidade;
    private String unidade;
    private String observacoes;
    private Long receitaId;
    
    // Construtores
    public IngredienteDTO() {}
    
    public IngredienteDTO(String nome, Double quantidade, String unidade) {
        this.nome = nome;
        this.quantidade = quantidade;
        this.unidade = unidade;
    }
    
    public IngredienteDTO(String nome, Double quantidade, String unidade, Long receitaId) {
        this(nome, quantidade, unidade);
        this.receitaId = receitaId;
    }
    
    // Getters e Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNome() {
        return nome;
    }
    
    public void setNome(String nome) {
        this.nome = nome;
    }
    
    public Double getQuantidade() {
        return quantidade;
    }
    
    public void setQuantidade(Double quantidade) {
        this.quantidade = quantidade;
    }
    
    public String getUnidade() {
        return unidade;
    }
    
    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }
    
    public String getObservacoes() {
        return observacoes;
    }
    
    public void setObservacoes(String observacoes) {
        this.observacoes = observacoes;
    }
    
    public Long getReceitaId() {
        return receitaId;
    }
    
    public void setReceitaId(Long receitaId) {
        this.receitaId = receitaId;
    }

    @Override
    public String toString() {
        return "IngredienteDTO{" +
                "id=" + id +
                ", nome='" + nome + '\'' +
                ", quantidade=" + quantidade +
                ", unidade='" + unidade + '\'' +
                ", receitaId=" + receitaId +
                '}';
    }
}
