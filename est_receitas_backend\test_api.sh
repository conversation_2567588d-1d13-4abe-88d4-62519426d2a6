#!/bin/bash

# Script para testar a API EST Receitas
# Certifique-se de que o backend está a correr na porta 8080

BASE_URL="http://localhost:8080/api"
TOKEN=""


# Função para fazer requests
make_request() {
    local method=$1
    local url=$2
    local data=$3
    local headers=$4
    
    echo "📡 $method $url"
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            curl -s -X $method "$url" -H "Content-Type: application/json" -H "$headers" -d "$data" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"
        else
            curl -s -X $method "$url" -H "Content-Type: application/json" -d "$data" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"
        fi
    else
        if [ -n "$headers" ]; then
            curl -s -X $method "$url" -H "$headers" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"
        else
            curl -s -X $method "$url" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"
        fi
    fi
    echo ""
}

# 1. Verificar Saúde
echo "1️⃣ VERIFICAR SAÚDE"
make_request "GET" "$BASE_URL/testes/saude"

# 2. Registar Utilizador
echo "REGISTAR UTILIZADOR"
REGISTO_DATA='{
  "nome": "João Silva",
  "email": "<EMAIL>",
  "password": "123456",
  "confirmPassword": "123456"
}'
REGISTO_RESPONSE=$(curl -s -X POST "$BASE_URL/autenticacao/registo" -H "Content-Type: application/json" -d "$REGISTO_DATA")
echo "$REGISTO_RESPONSE" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"

# Extrair token da resposta
TOKEN=$(echo "$REGISTO_RESPONSE" | jq -r '.token' 2>/dev/null)
if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
    echo "Token obtido: ${TOKEN:0:50}..."
else
    echo "Erro ao obter token"
fi
echo ""

# 3. Login (caso o registo falhe)
if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
    echo "LOGIN (fallback)"
    LOGIN_DATA='{
      "email": "<EMAIL>",
      "password": "123456"
    }'
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/autenticacao/login" -H "Content-Type: application/json" -d "$LOGIN_DATA")
    echo "$LOGIN_RESPONSE" | jq '.' 2>/dev/null || echo "Resposta não é JSON válido"
    
    TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.token' 2>/dev/null)
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
        echo "Token obtido via login: ${TOKEN:0:50}..."
    else
        echo "Erro ao obter token via login"
    fi
    echo ""
fi

# Se temos token, testar endpoints protegidos
if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
    AUTH_HEADER="Authorization: Bearer $TOKEN"
    
    # 4. Verificar Token
    echo "VERIFICAR TOKEN"
    make_request "POST" "$BASE_URL/autenticacao/verificar" "" "$AUTH_HEADER"

    # 5. Obter Perfil
    echo "OBTER PERFIL"
    make_request "GET" "$BASE_URL/autenticacao/perfil" "" "$AUTH_HEADER"
    
    # 6. Atualizar Perfil
    echo "6ATUALIZAR PERFIL"
    UPDATE_DATA='{
      "nome": "João Silva Santos",
      "email": "<EMAIL>"
    }'
    make_request "PUT" "$BASE_URL/autenticacao/perfil" "$UPDATE_DATA" "$AUTH_HEADER"

    # 8. Logout
    echo "LOGOUT"
    make_request "POST" "$BASE_URL/autenticacao/logout" "" "$AUTH_HEADER"
else
    echo "Não foi possível obter token - saltando testes protegidos"
fi

# 9. Verificar Email
echo "VERIFICAR EMAIL"
make_request "GET" "$BASE_URL/autenticacao/verificar-email?email=<EMAIL>"

# 10. Testar Receitas
echo "TESTAR RECEITAS"
make_request "GET" "$BASE_URL/receitas"

# 11. Testar Stock
echo "TESTAR STOCK"
make_request "GET" "$BASE_URL/stock"

echo "✅ TESTES CONCLUÍDOS!"
echo "================================"
echo ""
echo "📋 RESUMO:"
echo "- Health Check: Verificar se retorna status OK"
echo "- Registo: Deve retornar token JWT"
echo "- Login: Deve retornar token JWT"
echo "- Perfil: Deve retornar dados do utilizador"
echo "- Receitas/Stock: Podem retornar listas vazias (normal)"
echo ""
echo "🔍 Para mais detalhes, use Postman ou browser para:"
echo "- H2 Console: http://localhost:8080/h2-console"
echo "- Verificar Saúde: http://localhost:8080/api/testes/saude"
echo "- Documentação: Veja API_DOCUMENTATION.md"
